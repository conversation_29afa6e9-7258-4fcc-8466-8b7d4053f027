import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:Nestblock/utils/app_colors.dart';

import '../../widgets/flexible_space_widget.dart';

class SliderScreen extends StatelessWidget {
  final String description;
  final String subDescription;
  final String image;

  const SliderScreen(
      {super.key,
      required this.description,
      required this.image,
      required this.subDescription});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: <Widget>[
        Center(
          child: Container(
              height: 140.h,
              width: double.maxFinite,
              child: Center(child: FlexibleSpaceWidget(isOnbording: true))),
        ),
        SizedBox(
          height: 72.h,
        ),
        Center(
          child: Column(
            children: [
              Image.asset(
                image,
                height: 160.h,
                width: 160.w,
              ),
              SizedBox(
                height: 40.h,
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 66.w),
                child: Center(
                  child: Text(description,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        height: 1.3,
                        fontSize: 30.sp,
                        color: AppColors.appBlackColor,
                        fontWeight: FontWeight.w500,
                      )),
                ),
              ),
            ],
          ),
        ),
        SizedBox(
          height: 24.h,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 40),
          child: Text(subDescription,
              textAlign: TextAlign.center,
              style: TextStyle(
                  fontSize: 16.sp,
                  color: AppColors.sliderSubDescriptionColor,
                  fontWeight: FontWeight.normal)),
        ),
      ],
    );
  }
}
