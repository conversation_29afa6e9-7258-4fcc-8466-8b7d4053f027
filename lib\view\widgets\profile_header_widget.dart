import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:Nestblock/controller/app_controller.dart';
import 'package:Nestblock/utils/local_storage.dart';
import '../../utils/app_colors.dart';

PreferredSizeWidget ProfileHeaderWidget({
  required Widget profilePhoto,
  required String profileName,
  required String joinedDate,
  bool? isProfileEdit = false,
  bool? isProfileEmpty = false,
  void Function()? onPressed,
  void Function()? onBackPressed,
}) {
  return PreferredSize(
    preferredSize:
        Platform.isAndroid ? Size.fromHeight(250.h) : Size.fromHeight(220.h),
    child: AppBar(
      backgroundColor: Get.find<AppController>().getDarkBgColor(),
      centerTitle: true,
      title: Text(
        LocalStorage.get(LocalStorage.languageData) == null ||
                LocalStorage.get(
                        LocalStorage.languageData)['Profile Settings'] ==
                    null
            ? "Profile Settings"
            : LocalStorage.get(LocalStorage.languageData)['Profile Settings'],
        style: TextStyle(
            fontSize: 24.sp,
            color: AppColors.appWhiteColor,
            fontWeight: FontWeight.w500),
        textAlign: TextAlign.center,
      ),
      leading: IconButton(
          onPressed: onBackPressed ??
              () {
                Get.back();
              },
          icon: Icon(
            Icons.arrow_back,
            color: AppColors.appWhiteColor,
          )),
      flexibleSpace: Container(
        height: 320.h,
        // height: 320.h,
        width: double.maxFinite,
        decoration: BoxDecoration(
          color: AppColors.appPrimaryColor,
          borderRadius: BorderRadius.vertical(bottom: Radius.circular(18.r)),
        ),
        child: Stack(
          children: [
            Positioned(
              bottom: 0,
              right: -35.w,
              child: Container(
                alignment: Alignment.centerRight,
                height: 220.h,
                width: 180.w,
                decoration: BoxDecoration(
                    image: DecorationImage(
                  image: AssetImage(
                    "assets/images/round_horiz.png",
                  ),
                  fit: BoxFit.fitHeight,
                )),
              ),
            ),
            Positioned(
              bottom: -55.h,
              right: 50.w,
              child: Container(
                height: 220.h,
                width: 200.w,
                decoration: BoxDecoration(
                    image: DecorationImage(
                  image: AssetImage(
                    "assets/images/round_verti.png",
                  ),
                  fit: BoxFit.fitWidth,
                )),
              ),
            ),
            Positioned(
              top: 80.h,
              left: 0,
              right: 0,
              child: Column(
                children: [
                  SizedBox(
                    height: 16.h,
                  ),
                  Stack(
                    children: [
                      profilePhoto,

                      // ClipOval(
                      //     child: CachedNetworkImage(
                      //   imageUrl: profilePhotoPath,
                      //   height: 80.0.h,
                      //   width: 80.0.h,
                      //   fit: BoxFit.cover,
                      //   placeholder: (context, url) =>
                      //       const CircularProgressIndicator(), // Placeholder while loading
                      //   errorWidget: (context, url, error) => const Icon(
                      //       Icons.error), // Error widget if loading fails
                      // )),
                      if (isProfileEdit == true)
                        Positioned(
                          bottom: isProfileEmpty == true ? 10.h : 0,
                          right: isProfileEmpty == true ? 5.h : 0,
                          child: InkResponse(
                            onTap: onPressed,
                            child: CircleAvatar(
                              radius: 12.r,
                              backgroundColor: AppColors.appWhiteColor,
                              child: Icon(
                                Icons.camera_alt_outlined,
                                color: Colors.black,
                                size: 17.h,
                              ),
                            ),
                          ),
                        )
                    ],
                  ),
                  SizedBox(
                    height: 10.h,
                  ),
                  Text(
                    profileName,
                    style: TextStyle(
                        fontSize: 20.sp,
                        color: AppColors.appWhiteColor,
                        fontWeight: FontWeight.w500),
                  ),
                  SizedBox(
                    height: 10.h,
                  ),
                  Text(
                    joinedDate,
                    style: TextStyle(
                        fontSize: 16.sp,
                        color: AppColors.appWhiteColor,
                        fontWeight: FontWeight.normal),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    ),
  );
}
