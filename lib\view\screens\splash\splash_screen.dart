import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:Nestblock/controller/auth_controller.dart';
import 'package:Nestblock/utils/app_colors.dart';
import '../../../controller/app_controller.dart';
import '../../../utils/local_storage.dart';
import '../auth/signin_screen.dart';
import '../landing/bottom_navbar.dart';
import '../onboarding/onboarding_screen.dart';
// import '../../../utils/local_storage.dart';

class SplashScreen extends StatefulWidget {
  static const String routeName = "/splashScreen";
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    Future.delayed(const Duration(seconds: 2), () {
      Get.find<AuthController>().getUserToken().toString().isEmpty
          ? LocalStorage.get(LocalStorage.isNewUser) != null &&
                  LocalStorage.get(LocalStorage.isNewUser) == false
              ? Get.offAllNamed(SignInScreen.routeName)
              : Get.offAllNamed(OnbordingScreen.routeName)
          : Get.offAllNamed(BottomNavBar.routeName);
    });
    Get.find<AppController>().getAppConfig();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appPrimaryColor30,
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            "Online Money \nTransfer",
            style: TextStyle(
                fontSize: 30.sp,
                color: AppColors.appWhiteColor,
                fontWeight: FontWeight.w500),
            textAlign: TextAlign.center,
          ),
          Stack(
            children: [
              Positioned(
                  top: 150.h,
                  left: 0,
                  right: 0,
                  child: Center(
                      child: Image.asset(
                    "assets/images/app_logo.png",
                    height: 80.h,
                    width: 80.w,
                  ))),
              Image.asset(
                "assets/images/splash_circle.png",
                fit: BoxFit.cover,
              ),
            ],
          ),
          //SizedBox(height: 40.h,),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                "Nestblock",
                style: GoogleFonts.rambla(
                    fontWeight: FontWeight.bold,
                    fontSize: 50.sp,
                    color: AppColors.appWhiteColor),
              ),
              Image.asset(
                "assets/images/app_logo.png",
                height: 40.h,
                width: 40.w,
              )
            ],
          ),
        ],
      ),
    );
  }
}
