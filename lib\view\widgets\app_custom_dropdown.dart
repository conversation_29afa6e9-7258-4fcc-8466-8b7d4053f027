import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:Nestblock/utils/app_colors.dart';

import '../../utils/local_storage.dart';

class AppCustomDropDown extends StatelessWidget {
  double? height;
  double? width;
  bool? advanced;
  String? hint;
  Color? hintColor;
  double? dropDownWidth;
  double? dropdownMaxHeight;
  double? itemHeight;
  List<dynamic> items;
  BoxDecoration? decoration;
  BoxDecoration? dropdownDecoration;
  String? selectedValue;
  TextStyle? hintStyle;
  double? fontSize;
  Color? textColor;
  Color? boxColor;
  Color? buttonBackground;
  Color? dropdownBoxBackground;
  TextStyle? selectedStyle;
  double? boxBorderRadius;
  double? paddingLeft;
  ValueChanged<dynamic>? onChanged;
  Color? bgColor;
  Color? selectedTextColor;

  AppCustomDropDown(
      {required this.items,
      @required this.onChanged,
      @required this.selectedValue,
      this.advanced = false,
      this.selectedStyle,
      this.height,
      this.decoration,
      this.dropdownDecoration,
      this.dropDownWidth,
      this.itemHeight,
      this.buttonBackground,
      this.dropdownBoxBackground,
      this.hint,
      this.width,
      this.textColor,
      this.boxColor,
      this.fontSize,
      this.dropdownMaxHeight,
      this.boxBorderRadius,
      this.paddingLeft,
      this.hintStyle,
      this.bgColor,
      this.selectedTextColor,
      this.hintColor,
      Key? key})
      : super(key: key);

  List<DropdownMenuItem<String>> _addDividersAfterItems(List<dynamic> items) {
    List<DropdownMenuItem<String>> _menuItems = [];
    for (var item in items) {
      _menuItems.addAll(
        [
          advanced == false
              ? DropdownMenuItem<String>(
                  value: item,
                  child: Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: paddingLeft ?? 16),
                    child: Text(
                      item,
                      style: selectedStyle ??
                          TextStyle(
                              fontSize: fontSize ?? 18,
                              fontWeight: FontWeight.w400,
                              color: textColor ?? Color(0xFF666666)),
                    ),
                  ),
                )
              : DropdownMenuItem<String>(
                  value: item,
                  child: Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: paddingLeft ?? 16),
                    child: Text(
                      item,
                      style: selectedStyle ??
                          TextStyle(
                              fontSize: fontSize ?? 18,
                              fontWeight: FontWeight.w400,
                              color: getColor(item) ?? AppColors.appBlackColor),
                    ),
                  ),
                ),
          //If it's last item, we will not add Divider after it.
          if (item != items.last)
            DropdownMenuItem<String>(
              enabled: false,
              child: Divider(
                thickness: 1,
                color: boxColor ?? Color(0xFFC7C7C7),
              ),
            ),
        ],
      );
    }
    return _menuItems;
  }

  Color? getColor(String value) {
    if (value.toLowerCase() == "paid") {
      return Color(0xFF33CC66);
    } else if (value.toLowerCase() == "pending") {
      return Color(0xFFECB409);
    } else if (value.toLowerCase() == "delivery") {
      return Color(0xFFED3E3E);
    } else {
      return textColor;
    }
  }

  List<double> _getCustomItemsHeights() {
    List<double> _itemsHeights = [];
    for (var i = 0; i < (items.length * 2) - 1; i++) {
      if (i.isEven) {
        _itemsHeights.add(40);
      }
      //Dividers indexes will be the odd indexes
      if (i.isOdd) {
        _itemsHeights.add(4);
      }
    }
    return _itemsHeights;
  }

  @override
  Widget build(BuildContext context) {
    return DropdownButtonHideUnderline(
      child: DropdownButton2(
        iconStyleData: IconStyleData(
          icon: Padding(
            padding: EdgeInsets.only(right: 15),
            child: Icon(
              Icons.arrow_drop_down,
              color: AppColors.appBlackColor,
            ),
          ),
        ),
        buttonStyleData: ButtonStyleData(
          decoration: decoration ??
              BoxDecoration(
                borderRadius: BorderRadius.circular(boxBorderRadius ?? 5),
              ),
          height: height ?? 60,
          width: width ?? 200,
        ),
        dropdownStyleData: DropdownStyleData(
          decoration: dropdownDecoration ??
              BoxDecoration(
                color: bgColor ?? AppColors.appWhiteColor,
                border: Border.all(
                  color: LocalStorage.get(LocalStorage.isDark) != null &&
                          LocalStorage.get(LocalStorage.isDark) == true
                      ? Colors.grey.shade700.withOpacity(.4)
                      : AppColors.appBlack40,
                ),
                borderRadius: BorderRadius.circular(boxBorderRadius ?? 5),
              ),
          maxHeight: dropdownMaxHeight ?? 200,
        ),
        isExpanded: true,
        hint: Padding(
          padding: EdgeInsets.symmetric(horizontal: paddingLeft ?? 16),
          child: Text(
            hint ?? 'Select Item',
            style: hintStyle ??
                TextStyle(
                  fontSize: fontSize ?? 16,
                  color: hintColor ?? Colors.grey,
                ),
          ),
        ),
        items: _addDividersAfterItems(items),

        // customItemsHeights: _getCustomItemsHeights(),
        value: selectedValue,
        onChanged: onChanged,
        // itemHeight: itemHeight ?? 60,
        menuItemStyleData: MenuItemStyleData(
          padding: const EdgeInsets.symmetric(horizontal: 8.0),
        ),
      ),
    );
  }
}
