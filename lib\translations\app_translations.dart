import 'package:Nestblock/translations/ar.dart';
import 'package:Nestblock/translations/en.dart';
import 'package:get/get.dart';

class AppTranslations extends Translations {
  Map<String, Map<String, String>> _translations = {};

  @override
  Map<String, Map<String, String>> get keys => _translations;

  void loadTranslations() {
    _translations['en_US'] = en_translation;
    _translations['ar_AR'] = ar_translation;
  }
}
