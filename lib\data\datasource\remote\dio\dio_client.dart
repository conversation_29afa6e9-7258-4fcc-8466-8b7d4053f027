import 'dart:io';

import 'package:Nestblock/routes/page_index.dart';
import 'package:dio/dio.dart' as d;
import 'package:flutter/foundation.dart';

import 'package:shared_preferences/shared_preferences.dart';

import 'logging_interceptor.dart';

class DioClient {
  final String baseUrl;
  final LoggingInterceptor loggingInterceptor;
  final SharedPreferences sharedPreferences;

  d.Dio? dio;
  String? token;
  String? countryCode;

  DioClient(
    this.baseUrl,
    d.Dio? dioC, {
    required this.loggingInterceptor,
    required this.sharedPreferences,
  }) {
    // token = sharedPreferences.getString(AppConstants.TOKEN);
    // countryCode=sharedPreferences.getString(AppConstants.COUNTRY_CODE);

    // print("......Token..... $token");

    dio = dioC ?? d.Dio();
    dio!
      ..options.baseUrl = baseUrl
      ..options.connectTimeout = 60000.milliseconds
      ..options.receiveTimeout = 60000.milliseconds
      ..httpClientAdapter
      ..options.headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
        //AppConstants.LANG_KEY : countryCode == 'US'? 'en':countryCode.toLowerCase(),
      };

    dio!.interceptors.add(loggingInterceptor);
  }

  void updateHeader(String token, String countryCode) {
    token = (token == null ? this.token : token)!;
    // countryCode = countryCode == null ? this.countryCode == 'US' ? 'en': this.countryCode.toLowerCase(): countryCode == 'US' ? 'en' : countryCode.toLowerCase();
    this.token = token;
    this.countryCode = countryCode;
    print('===Country code====>$countryCode');
    if (kDebugMode) {
      print('===Token====>$token');
    }
    dio?.options.headers = {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
      // AppConstants.LANG_KEY: countryCode == 'US'? 'en':countryCode.toLowerCase(),
    };
  }

  Future<d.Response> get(
    String uri, {
    Map<String, dynamic>? queryParameters,
    d.Options? options,
    d.CancelToken? cancelToken,
    d.ProgressCallback? onReceiveProgress,
  }) async {
    try {
      var response = await dio!.get(
        uri,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onReceiveProgress: onReceiveProgress,
      );
      return response;
    } on SocketException catch (e) {
      throw SocketException(e.toString());
    } on FormatException catch (_) {
      throw FormatException("Unable to process the data");
    } catch (e) {
      throw e;
    }
  }

  Future<d.Response> post(
    String uri, {
    data,
    Map<String, dynamic>? queryParameters,
    d.Options? options,
    d.CancelToken? cancelToken,
    d.ProgressCallback? onSendProgress,
    d.ProgressCallback? onReceiveProgress,
  }) async {
    try {
      var response = await dio!.post(
        uri,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );
      return response;
    } on FormatException catch (_) {
      throw FormatException("Unable to process the data");
    } catch (e) {
      throw e;
    }
  }

  Future<d.Response> put(
    String uri, {
    data,
    Map<String, dynamic>? queryParameters,
    d.Options? options,
    d.CancelToken? cancelToken,
    d.ProgressCallback? onSendProgress,
    d.ProgressCallback? onReceiveProgress,
  }) async {
    try {
      var response = await dio!.put(
        uri,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );
      return response;
    } on FormatException catch (_) {
      throw FormatException("Unable to process the data");
    } catch (e) {
      throw e;
    }
  }

  Future<d.Response> delete(
    String uri, {
    data,
    Map<String, dynamic>? queryParameters,
    d.Options? options,
    d.CancelToken? cancelToken,
  }) async {
    try {
      var response = await dio!.delete(
        uri,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
      return response;
    } on FormatException catch (_) {
      throw FormatException("Unable to process the data");
    } catch (e) {
      throw e;
    }
  }
}
