import 'package:Nestblock/view/screens/auth/signin_screen.dart';

import '../data/model/base_model/api_response.dart';
import '../data/repository/reply_ticket_repo.dart';
import '../routes/page_index.dart';
import '../utils/app_colors.dart';
import '../utils/helpers.dart';
import '../view/screens/profile/profile_setting_screen.dart';
import '../view/screens/profile/two_fa_screen.dart';
import '../view/widgets/app_bottom_sheet.dart';
import 'auth_controller.dart';

class ReplyTicketController extends GetxController {
  var message = TextEditingController();

  final ReplyTicketRepo replyTicketRepo;

  ReplyTicketController({required this.replyTicketRepo});

  bool _isLoading = false;
  bool get isLoading => _isLoading;

  Future<dynamic> ticketReplyRequest(
      dynamic id, dynamic message, dynamic replyTicket,
      {dynamic result, bool? isCloseTicket = false}) async {
    _isLoading = true;
    update();
    ApiResponse apiResponse = await replyTicketRepo
        .replyTicketRequest(id, message, replyTicket, result: result);

    if (apiResponse.response != null &&
        apiResponse.response!.statusCode == 200) {
      _isLoading = false;
      update();
      if (apiResponse.response!.data != null) {
        var res = apiResponse.response!.data;
        if (res['message'] == "Email Verification Required") {
          _isLoading = false;

          Helpers.showSnackBar(msg: res['message']);
          await appBottomSheet();
        } else if (res['message'] == "Mobile Verification Required") {
          _isLoading = false;
          Helpers.showSnackBar(msg: res['message']);
          await appBottomSheet(isMailVerification: false);
        } else if (res['message'] == "Two FA Verification Required") {
          _isLoading = false;
          Helpers.showSnackBar(msg: res['message']);
          Get.offAll(TwoFaScreen(isTwofaVerification: true));
        } else if (res['message'] == "Your account has been suspend") {
          _isLoading = false;
          Helpers.showSnackBar(msg: res['message']);
          Get.find<AuthController>().removeUserToken();
          Get.offAllNamed(SignInScreen.routeName);
        } else if (res['message'] == "Identity Verification Required") {
          _isLoading = false;
          Helpers.showSnackBar(msg: res['message']);
          Get.offAll(ProfileSettingScreen(isIdentityVerification: true));
        } else if (res['message'] == "Address Verification Required") {
          _isLoading = false;
          Helpers.showSnackBar(msg: res['message']);
          Get.offAll(ProfileSettingScreen(isAddressVerification: true));
        } else {
          if (isCloseTicket == true) {
            Helpers.showSnackBar(
                msg: apiResponse.response!.data['message'],
                bgColor: AppColors.appGreenColor);
          }
        }

        update();
      }
    } else {
      _isLoading = false;
      update();
    }
  }
}
