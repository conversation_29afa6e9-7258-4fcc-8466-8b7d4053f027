import 'package:Nestblock/routes/page_index.dart';

import '../data/model/base_model/api_response.dart';
import '../data/model/response/money_calculation_proceed_model.dart';
import '../data/model/response/transfer_form_model.dart' as transferForm;
import '../data/repository/money_calculation_proceed_repo.dart';
import '../utils/helpers.dart';

class MoneyCalculationProceedController extends GetxController {
  final MoneyCalculationProceedRepo moneyCalculationProceedRepo;

  MoneyCalculationProceedController(
      {required this.moneyCalculationProceedRepo});

  bool _isLoading = false;
  bool get isLoading => _isLoading;

  Data? _data;
  Data? get data => _data;

  // TEXTEDITING CONTROLLER
  var recipientName = TextEditingController();
  var recipientContactNo = TextEditingController();
  var recipientEmailAddr = TextEditingController();
  var promocode = TextEditingController();

  MoneyCalculationProceedModel moneyCalculationProceedModel =
      MoneyCalculationProceedModel();
  RxString invoiceNumber = "".obs;

  /// Country Service Data
  Future<dynamic> getMoneyCalculationData(
    dynamic amount,
    dynamic sendCountry,
    dynamic getCountry,
    dynamic facilitiesId,
    dynamic payoutNetwork,
    dynamic sendReceive,
  ) async {
    _isLoading = true;
    update();
    ApiResponse apiResponse =
        await moneyCalculationProceedRepo.getMoneyCalculationProceedData(amount,
            sendCountry, getCountry, facilitiesId, payoutNetwork, sendReceive);

    if (apiResponse.response != null &&
        apiResponse.response!.statusCode == 200) {
      _isLoading = false;
      update();
      if (apiResponse.response!.data != null) {
        update();
        moneyCalculationProceedModel =
            MoneyCalculationProceedModel.fromJson(apiResponse.response!.data!);
        _data = moneyCalculationProceedModel.data;
        invoiceNumber.value =
            moneyCalculationProceedModel.data!.sendMoney!.invoice.toString();
        PaymentGatewayController.to.invoiceNumber =
            moneyCalculationProceedModel.data!.sendMoney!.invoice.toString();

        PaymentGatewayController.to.totalBaseAmountPay =
            _data!.sendMoney!.totalBaseAmountPay.toStringAsFixed(2);
        PaymentGatewayController.to.amountTextEditingCtrlr.text =
            _data!.sendMoney!.totalBaseAmountPay.toStringAsFixed(2);
        PaymentGatewayController.to.sendCurrency =
            _data!.sendMoney!.sendCurr.toString();
        await getTransferFormData(invoice: invoiceNumber.value);
        Get.toNamed(RecipientsDetailsScreen.routeName);
        update();
      }
    } else {
      _isLoading = false;
      update();
    }
  }

  // GET TRANSFER FORM DATA
  List<transferForm.Data> transferFormData = [];
  List<DynamicFieldModel> dynamicFieldList = [];
  dynamic serviceId = -1;
  dynamic bankCode = "";
  bool isReciverCurrExistInFlutterwave = false;
  Future<dynamic> getTransferFormData({required String invoice}) async {
    _isLoading = true;
    update();
    ApiResponse apiResponse = await moneyCalculationProceedRepo
        .getTransferFormData(invoiceNumber: '$invoice');
    transferFormData = [];

    if (apiResponse.response != null &&
        apiResponse.response!.statusCode == 200) {
      _isLoading = false;
      update();
      if (apiResponse.response!.data != null) {
        //------ for account name verify in recipient details page
        var data = apiResponse.response!.data['data']['sendMoney'];
        serviceId = data['service_id'];
        if (data['provider']['bank_code'] != null) {
          bankCode = data['provider']['bank_code'];
        }

        if (apiResponse.response!.data['data']['flutterwave'] is List) {
          List flutterwaveList =
              apiResponse.response!.data['data']['flutterwave'];
          if (flutterwaveList.contains("${data['receive_curr']}")) {
            isReciverCurrExistInFlutterwave = true;
          } else {
            isReciverCurrExistInFlutterwave = false;
          }
        }
        // await getFlutterwaveData(data['receive_curr']);
        //-------

        transferFormData.add(
            transferForm.TransferFromModel.fromJson(apiResponse.response!.data!)
                .data!);
        Get.back();
        Get.toNamed(RecipientsDetailsScreen.routeName);
        if (apiResponse.response!.data['data']['form_dynamic_fields'] is Map) {
          Map form = apiResponse.response!.data['data']['form_dynamic_fields'];

          dynamicFieldList = [];
          form.forEach((key, value) {
            dynamicFieldList.add(DynamicFieldModel.fromJson(value));
          });
        }

        update();
      }
    } else {
      _isLoading = false;
      update();
    }
  }

  // Future<dynamic> getFlutterwaveData(receiveCurr) async {
  //   _isLoading = true;
  //   update();
  //   ApiResponse apiResponse =
  //       await moneyCalculationProceedRepo.getFlutterwaveData();

  //   if (apiResponse.response != null &&
  //       apiResponse.response!.statusCode == 200) {
  //     _isLoading = false;
  //     update();
  //     if (apiResponse.response!.data != null) {
  //       Map<String, dynamic> datas = apiResponse.response!.data;
  //       if (datas.containsKey(receiveCurr)) {
  //         isReciverCurrExistInFlutterwave = true;
  //       } else if (!datas.containsKey(receiveCurr)) {
  //         isReciverCurrExistInFlutterwave = false;
  //       }
  //     }
  //   } else {
  //     _isLoading = false;
  //     update();
  //   }
  // }

  bool isFormsubmitting = false;
  Future<dynamic> postTransferForm(
      {required Map<String, dynamic> body,
      required BuildContext context}) async {
    isFormsubmitting = true;
    update();
    ApiResponse apiResponse =
        await moneyCalculationProceedRepo.submitTransferForm(
            data: body, invoiceNumber: '${invoiceNumber.value}');

    if (apiResponse.response != null &&
        apiResponse.response!.statusCode == 200) {
      isFormsubmitting = false;
      update();
      if (apiResponse.response!.data != null) {
        Map<String, dynamic> response = apiResponse.response!.data;
        bool hasError = response.containsKey('errors');
        if (hasError) {
          Map errorData = apiResponse.response!.data['errors'];
          errorData.forEach((field, errors) {
            final errorMessages = errors
                .map((error) => Center(
                      child: Text(
                        error,
                        style: TextStyle(
                            color: AppColors.appRedColor, fontFamily: "Dubai"),
                      ),
                    ))
                .toList();
            final snackBar = SnackBar(
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ...errorMessages,
                ],
              ),
            );
            ScaffoldMessenger.of(context).showSnackBar(snackBar);
          });
        } else {
          Helpers.showSnackBar(
              msg: response['message'], bgColor: AppColors.appGreenColor);
          if (response['status'] == true) {
            Get.to(() => PaymentMethodScreen());
          }
        }

        update();
      }
    } else {
      isFormsubmitting = false;
      update();
    }
  }

  String accountName = "";
  Future<dynamic> verifyAccount(
      {required Map<String, dynamic> body,
      required BuildContext context}) async {
    isFormsubmitting = true;
    update();
    ApiResponse apiResponse =
        await moneyCalculationProceedRepo.vefifyAccount(data: body);

    if (apiResponse.response != null &&
        apiResponse.response!.statusCode == 200) {
      isFormsubmitting = false;
      update();
      if (apiResponse.response!.data != null) {
        Map<String, dynamic> response = apiResponse.response!.data;
        bool hasError = response.containsKey('errors');
        if (hasError) {
          Map errorData = apiResponse.response!.data['errors'];
          errorData.forEach((field, errors) {
            final errorMessages = errors
                .map((error) => Center(
                      child: Text(
                        error,
                        style: TextStyle(
                            color: AppColors.appRedColor, fontFamily: "Dubai"),
                      ),
                    ))
                .toList();
            final snackBar = SnackBar(
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ...errorMessages,
                ],
              ),
            );
            ScaffoldMessenger.of(context).showSnackBar(snackBar);
          });
        } else {
          if (response['status'] == 'success') {
            Helpers.showSnackBar(
                msg: response['message'], bgColor: AppColors.appGreenColor);
            accountName = response['data']['account_name'];
          } else {
            Helpers.showSnackBar(
                msg: response['message'], bgColor: AppColors.appRedColor);
          }
        }

        update();
      }
    } else {
      isFormsubmitting = false;
      update();
    }
  }
}

class DynamicFieldModel {
  dynamic fieldName;
  dynamic fieldLevel;
  dynamic type;
  dynamic fieldLength;
  dynamic lengthType;
  dynamic validation;

  DynamicFieldModel({
    this.fieldName,
    this.fieldLevel,
    this.type,
    this.fieldLength,
    this.lengthType,
    this.validation,
  });

  factory DynamicFieldModel.fromJson(Map<String, dynamic> json) =>
      DynamicFieldModel(
        fieldName: json["field_name"],
        fieldLevel: json["field_level"],
        type: json["type"],
        fieldLength: json["field_length"],
        lengthType: json["length_type"],
        validation: json["validation"],
      );

  Map<String, dynamic> toJson() => {
        "field_name": fieldName,
        "field_level": fieldLevel,
        "type": type,
        "field_length": fieldLength,
        "length_type": lengthType,
        "validation": validation,
      };
}
