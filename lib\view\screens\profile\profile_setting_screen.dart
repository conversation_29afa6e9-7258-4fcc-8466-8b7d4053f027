import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:Nestblock/controller/app_controller.dart';
import 'package:Nestblock/controller/auth_controller.dart';
import 'package:Nestblock/controller/user_controller.dart';
import 'package:Nestblock/utils/helpers.dart';
import 'package:Nestblock/view/screens/auth/signin_screen.dart';
import 'package:Nestblock/view/screens/landing/bottom_navbar.dart';
import 'package:Nestblock/view/screens/profile/password_setting_screen.dart';
import 'package:Nestblock/view/screens/profile/profile_edit_screen.dart';
import '../../../utils/app_colors.dart';
import '../../../utils/local_storage.dart';
import '../../widgets/profile_header_widget.dart';
import '../verification/address_verification_screen.dart';
import '../verification/identity_verification_screen.dart';
import 'language_screen.dart';

class ProfileSettingScreen extends StatelessWidget {
  final bool? isIdentityVerification;
  final bool? isAddressVerification;
  static const String routeName = "/profileSettingScreen";
  const ProfileSettingScreen(
      {super.key,
      this.isIdentityVerification = false,
      this.isAddressVerification = false});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AppController>(builder: (_) {
      var storedLanguage = LocalStorage.get(LocalStorage.languageData) ?? {};
      return GetBuilder<UserController>(builder: (userController) {
        // _.getThemeColor();
        return WillPopScope(
          onWillPop: () async {
            if (isIdentityVerification == true ||
                isAddressVerification == true) {
              Get.offAllNamed(BottomNavBar.routeName);
            } else {
              Get.back();
            }
            return true;
          },
          child: Scaffold(
            backgroundColor: _.getDarkBgColor(),
            appBar: ProfileHeaderWidget(
                onBackPressed: () {
                  if (isIdentityVerification == true ||
                      isAddressVerification == true) {
                    Get.offAllNamed(BottomNavBar.routeName);
                  } else {
                    Get.back();
                  }
                },
                profilePhoto: userController.data == null
                    ? Container(
                        height: 40.h, width: 40.h, child: Helpers.appLoader())
                    : ClipOval(
                        child: (userController.data!.user!.image != null)
                            ? CachedNetworkImage(
                                imageUrl: "${userController.data!.user!.photo}",
                                height: 80.h,
                                width: 80.0.h,
                                fit: BoxFit.cover,
                                placeholder: (context, url) =>
                                    const CircularProgressIndicator(), // Placeholder while loading
                                errorWidget: (context, url, error) =>
                                    const Icon(Icons
                                        .error), // Error widget if loading fails
                              )
                            : Icon(Icons.account_circle, size: 80.h)),
                profileName: userController.data == null
                    ? ""
                    : "${userController.data!.user!.firstname} ${userController.data!.user!.lastname}",
                joinedDate: userController.data == null
                    ? ""
                    : "${storedLanguage['Joined At'] ?? "Joined At"} ${DateFormat('dd MMM yyyy').format(DateTime.parse(userController.data!.user!.createdAt).toLocal())}"),
            body: ListView(
              children: [
                SizedBox(height: 10.h),
                Card(
                  color: _.getDarkCardColorDefault(),
                  child: Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: 32.w, vertical: 12.h),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          storedLanguage['Theme'] ?? "Theme",
                          style: TextStyle(
                            fontFamily: "Dubai",
                            fontSize: 16.sp,
                            color: AppColors.appBlackColor,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        SizedBox(height: 10.w),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Icon(
                                  Icons.dark_mode_outlined,
                                  size: 17.h,
                                  color: AppColors.appBlackColor,
                                ),
                                SizedBox(width: 5.w),
                                Text(
                                  storedLanguage['Light/Dark'] ?? "Light/Dark",
                                  style: TextStyle(
                                    fontFamily: "Dubai",
                                    fontSize: 14.sp,
                                    color: AppColors.appBlackColor,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ],
                            ),
                            Transform.scale(
                                scale: .8,
                                child: Switch(
                                  activeColor:
                                      LocalStorage.get(LocalStorage.isDark) !=
                                                  null &&
                                              LocalStorage.get(
                                                      LocalStorage.isDark) ==
                                                  true
                                          ? Colors.white
                                          : Theme.of(context).disabledColor,
                                  inactiveTrackColor: Colors.grey,
                                  activeTrackColor:
                                      LocalStorage.get(LocalStorage.isDark) !=
                                                  null &&
                                              LocalStorage.get(
                                                      LocalStorage.isDark) ==
                                                  true
                                          ? AppColors.appPrimaryColor
                                          : Colors.grey,
                                  materialTapTargetSize:
                                      MaterialTapTargetSize.shrinkWrap,
                                  value:
                                      LocalStorage.get(LocalStorage.isDark) ??
                                          false,
                                  onChanged: _.themeManager,
                                )),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: 10.h),
                Padding(
                  padding:
                      EdgeInsets.symmetric(horizontal: 32.w, vertical: 12.h),
                  child: GestureDetector(
                    onTap: () {
                      Get.toNamed(ProfileEditScreen.routeName);
                    },
                    child: Container(
                      color: Colors.transparent,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              Image.asset(
                                "assets/images/profile_info.png",
                                height: 18.h,
                                width: 18.w,
                              ),
                              SizedBox(
                                width: 8.w,
                              ),
                              Text(
                                storedLanguage['Profile Information'] ??
                                    "Profile Information",
                                style: TextStyle(
                                    fontSize: 18.sp,
                                    color: AppColors.appBlackColor,
                                    fontWeight: FontWeight.normal),
                              ),
                            ],
                          ),
                          CircleAvatar(
                            backgroundColor: AppColors.appPrimaryColor,
                            radius: 12.r,
                            child: Icon(
                              Icons.arrow_forward_ios_outlined,
                              color: AppColors.appWhiteColor,
                              size: 10.h,
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: 32.w,
                  ),
                  child: Divider(
                    color: AppColors.appBlack10,
                  ),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 32.w, vertical: 8),
                  child: GestureDetector(
                    onTap: () {
                      Get.toNamed(PasswordSettingScreen.routeName);
                    },
                    child: Container(
                      color: Colors.transparent,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              Image.asset(
                                "assets/images/password_change.png",
                                height: 18.h,
                                width: 18.w,
                              ),
                              SizedBox(
                                width: 8.w,
                              ),
                              Text(
                                storedLanguage['Password Setting'] ??
                                    "Password Setting",
                                style: TextStyle(
                                    fontSize: 18.sp,
                                    color: AppColors.appBlackColor,
                                    fontWeight: FontWeight.normal),
                              ),
                            ],
                          ),
                          CircleAvatar(
                            backgroundColor: AppColors.appPrimaryColor,
                            radius: 12.r,
                            child: Icon(
                              Icons.arrow_forward_ios_outlined,
                              color: AppColors.appWhiteColor,
                              size: 10.h,
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 32.w, vertical: 4),
                  child: Divider(
                    color: AppColors.appBlack10,
                  ),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 32.w, vertical: 8),
                  child: GestureDetector(
                    onTap: () {
                      Get.toNamed(LanguageScreen.routeName);
                    },
                    child: Container(
                      color: Colors.transparent,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              Image.asset(
                                "assets/images/language_change.png",
                                height: 18.h,
                                width: 18.h,
                              ),
                              SizedBox(
                                width: 8.w,
                              ),
                              Text(
                                storedLanguage['Language'] ?? "Language",
                                style: TextStyle(
                                    fontSize: 18.sp,
                                    color: AppColors.appBlackColor,
                                    fontWeight: FontWeight.normal),
                              ),
                            ],
                          ),
                          CircleAvatar(
                            backgroundColor: AppColors.appPrimaryColor,
                            radius: 12.r,
                            child: Icon(
                              Icons.arrow_forward_ios_outlined,
                              color: AppColors.appWhiteColor,
                              size: 10.h,
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 32.w, vertical: 4),
                  child: Divider(
                    color: AppColors.appBlack10,
                  ),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 32.w, vertical: 8),
                  child: GestureDetector(
                    onTap: () {
                      Get.to(() => IdentityVerificationScreen());
                    },
                    child: Container(
                      color: Colors.transparent,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              Image.asset(
                                "assets/images/identity_verification.png",
                                height: 18.h,
                                width: 18.w,
                              ),
                              SizedBox(
                                width: 8.w,
                              ),
                              Text(
                                storedLanguage['Identity Verification'] ??
                                    "Identity Verification",
                                style: TextStyle(
                                    fontSize: 18.sp,
                                    color: AppColors.appBlackColor,
                                    fontWeight: FontWeight.normal),
                              ),
                            ],
                          ),
                          CircleAvatar(
                            backgroundColor: AppColors.appPrimaryColor,
                            radius: 12.r,
                            child: Icon(
                              Icons.arrow_forward_ios_outlined,
                              color: AppColors.appWhiteColor,
                              size: 10.h,
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 32.w, vertical: 4),
                  child: Divider(
                    color: AppColors.appBlack10,
                  ),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 32.w, vertical: 8),
                  child: GestureDetector(
                    onTap: () {
                      Get.to(() => AddressVerificationScreen());
                    },
                    child: Container(
                      color: Colors.transparent,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              Image.asset(
                                "assets/images/address_verification.png",
                                height: 18.h,
                                width: 18.w,
                              ),
                              SizedBox(
                                width: 8.w,
                              ),
                              Text(
                                storedLanguage['Address Verification'] ??
                                    "Address Verification",
                                style: TextStyle(
                                    fontSize: 18.sp,
                                    color: AppColors.appBlackColor,
                                    fontWeight: FontWeight.normal),
                              ),
                            ],
                          ),
                          CircleAvatar(
                            backgroundColor: AppColors.appPrimaryColor,
                            radius: 12.r,
                            child: Icon(
                              Icons.arrow_forward_ios_outlined,
                              color: AppColors.appWhiteColor,
                              size: 10.h,
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 32.w, vertical: 4),
                  child: Divider(
                    color: AppColors.appBlack10,
                  ),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 32.w, vertical: 8),
                  child: GestureDetector(
                    onTap: () {
                      showDialog(
                        barrierDismissible: false,
                        context: context,
                        builder: (BuildContext context) {
                          return CupertinoAlertDialog(
                            title: Text(
                              storedLanguage['Alert'] ?? "Alert!",
                              style: TextStyle(
                                  fontSize: 20.sp,
                                  color: AppColors.appBlackColor,
                                  fontWeight: FontWeight.w500),
                            ),
                            content: Text(
                              storedLanguage['Do you want to Log Out?'] ??
                                  "Do you want to Log Out?",
                              style: TextStyle(
                                  fontSize: 18.sp,
                                  color: AppColors.appBlackColor,
                                  fontWeight: FontWeight.normal),
                            ),
                            actions: [
                              MaterialButton(
                                  onPressed: () {
                                    Navigator.of(context).pop();
                                  },
                                  child: Text(
                                    storedLanguage['No'] ?? "No",
                                    style: TextStyle(
                                        fontSize: 18.sp,
                                        color: AppColors.appBlackColor,
                                        fontWeight: FontWeight.normal),
                                  )),
                              MaterialButton(
                                  onPressed: () async {
                                    Get.find<AuthController>()
                                        .removeUserToken();
                                    await Get.offNamedUntil(
                                        SignInScreen.routeName,
                                        (route) => false);
                                  },
                                  child: Text(
                                    storedLanguage['Yes'] ?? "Yes",
                                    style: TextStyle(
                                        fontSize: 18.sp,
                                        color: AppColors.appBlackColor,
                                        fontWeight: FontWeight.normal),
                                  )),
                            ],
                          );
                        },
                      );
                    },
                    child: Container(
                      color: Colors.transparent,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              Image.asset(
                                "assets/images/logout_icon.png",
                                height: 18.h,
                                width: 18.w,
                              ),
                              SizedBox(
                                width: 8.w,
                              ),
                              Text(
                                storedLanguage['Logout'] ?? "Log out",
                                style: TextStyle(
                                    fontSize: 18.sp,
                                    color: AppColors.appBlackColor,
                                    fontWeight: FontWeight.normal),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                SizedBox(height: 30.h),
              ],
            ),
          ),
        );
      });
    });
  }
}
