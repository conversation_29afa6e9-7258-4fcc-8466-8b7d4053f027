import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:Nestblock/view/widgets/app_button.dart';

import '../../controller/app_controller.dart';
import '../../utils/app_colors.dart';

appDialog({
  BuildContext? context,
  required String firstFieldName,
  String? secondFieldName,
  required String thirdFieldName,
  required void Function() onSearchTap,
  void Function()? onCancelTap,
  required void Function() onDateTimeTap,
  required TextEditingController firstFieldController,
  TextEditingController? secondFieldController,
  required TextEditingController thirdFieldController,
  bool? isTransferLogSearch = false,
  bool? isHideStatusTextfield = false,
  String? receiveAmountName,
  TextEditingController? receiveAmountField,
}) {
  return Get.defaultDialog(
    radius: 20,
    contentPadding: EdgeInsets.symmetric(horizontal: 0),
    titlePadding: EdgeInsets.zero,
    title: "",
    backgroundColor: Get.find<AppController>().getDarkBgColor(),
    content: Container(
      width: double.maxFinite,
      color: Get.find<AppController>().getDarkBgColor(),
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Align(
            alignment: Alignment.centerRight,
            child: IconButton(
                padding: EdgeInsets.zero,
                constraints: BoxConstraints(),
                onPressed: onCancelTap ??
                    () {
                      Get.back();
                    },
                icon: Icon(
                  Icons.cancel,
                  color: Colors.black,
                )),
          ),
          Text(
            firstFieldName,
            style: TextStyle(
                fontSize: 16.sp,
                color: AppColors.appBlack50,
                fontWeight: FontWeight.w400),
          ),
          SizedBox(
            height: 2.h,
          ),
          SizedBox(
            height: 35.h,
            child: TextField(
              controller: firstFieldController,
              decoration: InputDecoration(
                contentPadding:
                    const EdgeInsets.symmetric(vertical: 0, horizontal: 16),
                filled: true, // Fill the background with color
                hintStyle: TextStyle(
                  color: AppColors.appBlack30,
                ),
                fillColor: Get.find<AppController>()
                    .getDarkBgTextFieldColorDefault(), // Background color
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                    color: Get.find<AppController>()
                        .getDarkBgTextFieldEnableBorderColorDefault(),
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: AppColors.appPrimaryColor),
                ),
              ),
              style: TextStyle(color: AppColors.appBlackColor),
            ),
          ),
          SizedBox(height: isHideStatusTextfield == true ? 0 : 10.h),
          isHideStatusTextfield == true
              ? SizedBox()
              : Text(
                  secondFieldName ?? "",
                  style: TextStyle(
                      fontSize: 16.sp,
                      color: AppColors.appBlack50,
                      fontWeight: FontWeight.w400),
                ),
          SizedBox(
            height: isHideStatusTextfield == true ? 0 : 2.h,
          ),
          isHideStatusTextfield == true
              ? SizedBox()
              : SizedBox(
                  height: 35.h,
                  child: TextField(
                    controller: secondFieldController,
                    decoration: InputDecoration(
                      contentPadding: const EdgeInsets.symmetric(
                          vertical: 0, horizontal: 16),
                      filled: true, // Fill the background with color
                      hintStyle: TextStyle(
                        color: AppColors.appBlack30,
                      ),
                      fillColor: Get.find<AppController>()
                          .getDarkBgTextFieldColorDefault(), // Background color
                      enabledBorder: OutlineInputBorder(
                        borderSide: BorderSide(
                          color: Get.find<AppController>()
                              .getDarkBgTextFieldEnableBorderColorDefault(),
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderSide:
                            BorderSide(color: AppColors.appPrimaryColor),
                      ),
                    ),
                    style: TextStyle(color: AppColors.appBlackColor),
                  ),
                ),
          SizedBox(height: 10.h),
          if (isTransferLogSearch == true) ...[
            Text(
              receiveAmountName ?? "",
              style: TextStyle(
                  fontSize: 16.sp,
                  color: AppColors.appBlack50,
                  fontWeight: FontWeight.w400),
            ),
            SizedBox(
              height: 2.h,
            ),
            SizedBox(
              height: 35.h,
              child: TextField(
                controller: receiveAmountField,
                decoration: InputDecoration(
                  contentPadding:
                      const EdgeInsets.symmetric(vertical: 0, horizontal: 16),
                  filled: true, // Fill the background with color
                  hintStyle: TextStyle(
                    color: AppColors.appBlack30,
                  ),
                  fillColor: Get.find<AppController>()
                      .getDarkBgTextFieldColorDefault(), // Background color
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                      color: Get.find<AppController>()
                          .getDarkBgTextFieldEnableBorderColorDefault(),
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: AppColors.appPrimaryColor),
                  ),
                ),
                style: TextStyle(color: AppColors.appBlackColor),
              ),
            ),
          ],
          SizedBox(height: 10.h),
          Text(
            thirdFieldName,
            style: TextStyle(
                fontSize: 16.sp,
                color: AppColors.appBlack50,
                fontWeight: FontWeight.w400),
          ),
          SizedBox(
            height: 4.h,
          ),
          GestureDetector(
            onTap: onDateTimeTap,
            child: SizedBox(
              height: 35.h,
              child: AbsorbPointer(
                absorbing: true,
                child: TextField(
                  controller: thirdFieldController,
                  decoration: InputDecoration(
                    contentPadding:
                        const EdgeInsets.symmetric(vertical: 0, horizontal: 16),
                    filled: true, // Fill the background with color
                    hintStyle: TextStyle(
                      color: AppColors.appBlack30,
                    ),
                    fillColor: Get.find<AppController>()
                        .getDarkBgTextFieldColorDefault(), // Background color
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(
                        color: Get.find<AppController>()
                            .getDarkBgTextFieldEnableBorderColorDefault(),
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: AppColors.appPrimaryColor),
                    ),
                  ),
                  style: TextStyle(color: AppColors.appBlackColor),
                ),
              ),
            ),
          ),
          SizedBox(height: 20.h),
          Row(
            children: [
              Expanded(
                child: AppButton(
                  onTap: onSearchTap,
                  bgColor: AppColors.appPrimaryColor,
                  textColor: AppColors.appWhiteColor,
                  text: "Search",
                ),
              ),
            ],
          ),
          SizedBox(height: 20.h),
        ],
      ),
    ),
  );
}
