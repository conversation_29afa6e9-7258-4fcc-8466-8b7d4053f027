import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:Nestblock/controller/user_controller.dart';

import '../../../controller/app_controller.dart';
import '../../../utils/app_colors.dart';
import '../../../utils/local_storage.dart';
import '../../widgets/profile_header_widget.dart';

class PasswordSettingScreen extends StatelessWidget {
  static const String routeName = "/passwordSetting";
  PasswordSettingScreen({super.key});

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    var storedLanguage = LocalStorage.get(LocalStorage.languageData) ?? {};
    return GetBuilder<UserController>(builder: (userController) {
      return Scaffold(
        backgroundColor: Get.find<AppController>().getDarkBgColor(),
        appBar: ProfileHeaderWidget(
            profilePhoto: ClipOval(
              child: userController.data != null &&
                      (userController.data!.user!.image != null)
                  ? CachedNetworkImage(
                      imageUrl: "${userController.data!.user!.photo}",
                      height: 80.0.h,
                      width: 80.0.h,
                      fit: BoxFit.cover,
                      placeholder: (context, url) =>
                          const CircularProgressIndicator(), // Placeholder while loading
                      errorWidget: (context, url, error) => const Icon(
                          Icons.error), // Error widget if loading fails
                    )
                  : Icon(Icons.account_circle, size: 80.h),
            ),
            profileName: userController.data == null
                ? ""
                : "${userController.data!.user!.firstname} ${userController.data!.user!.lastname}",
            joinedDate: userController.data == null
                ? ""
                : "${storedLanguage['Joined At'] ?? "Joined At"} ${DateFormat('dd MMM yyyy').format(DateTime.parse(userController.data!.user!.createdAt).toLocal())}"),
        body: ListView(
          children: [
            SizedBox(
              height: 20.h,
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 32.w),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      storedLanguage['Password Settings'] ??
                          "Password Settings",
                      style: TextStyle(
                          fontSize: 20.sp,
                          color: AppColors.appBlackColor,
                          fontWeight: FontWeight.w400),
                    ),
                    SizedBox(
                      height: 24.h,
                    ),
                    Text(
                      storedLanguage['Current Password'] ?? "Current Password",
                      style: TextStyle(
                          fontSize: 16.sp,
                          color: AppColors.appBlack50,
                          fontWeight: FontWeight.w400),
                    ),
                    SizedBox(
                      height: 4.h,
                    ),
                    TextFormField(
                      validator: (value) {
                        if (value!.isEmpty) {
                          return 'Current password is required';
                        }
                        return null; // Return null if the input is valid.
                      },
                      obscureText:
                          !userController.isCurrentPasswordVisible.value,
                      controller: userController.currentPassword,
                      decoration: InputDecoration(
                        suffixIcon: GestureDetector(
                          onTap: () {
                            userController.toggleCurrentPasswordVisibility();
                          },
                          child: Icon(
                            userController.isCurrentPasswordVisible.value
                                ? Icons.visibility
                                : Icons.visibility_off,
                            color: AppColors.appBlack70,
                          ),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                            vertical: 0, horizontal: 16),
                        filled: true, // Fill the background with color
                        hintStyle: TextStyle(
                          color: AppColors.appBlack30,
                        ),
                        fillColor: Get.find<AppController>()
                            .getDarkBgTextFieldColorDefault(), // Background color
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                            color: Get.find<AppController>()
                                .getDarkBgTextFieldEnableBorderColorDefault(),
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderSide:
                              BorderSide(color: AppColors.appPrimaryColor),
                        ),
                      ),
                      style: TextStyle(color: AppColors.appBlackColor),
                    ),
                    SizedBox(
                      height: 12.h,
                    ),
                    Text(
                      storedLanguage['New Password'] ?? "New Password",
                      style: TextStyle(
                          fontSize: 16.sp,
                          color: AppColors.appBlack50,
                          fontWeight: FontWeight.w400),
                    ),
                    SizedBox(
                      height: 4.h,
                    ),
                    TextFormField(
                      validator: (value) {
                        if (value!.isEmpty) {
                          return 'New password is required';
                        } else if (value.length < 5) {
                          return 'The password must be at least 5 characters.';
                        }
                        return null; // Return null if the input is valid.
                      },
                      obscureText: !userController.isPasswordVisible.value,
                      controller: userController.password,
                      decoration: InputDecoration(
                        suffixIcon: GestureDetector(
                          onTap: () {
                            userController.togglePasswordVisibility();
                          },
                          child: Icon(
                            userController.isPasswordVisible.value
                                ? Icons.visibility
                                : Icons.visibility_off,
                            color: AppColors.appBlack70,
                          ),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                            vertical: 0, horizontal: 16),
                        filled: true, // Fill the background with color
                        hintStyle: TextStyle(
                          color: AppColors.appBlack30,
                        ),
                        fillColor: Get.find<AppController>()
                            .getDarkBgTextFieldColorDefault(), // Background color
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                            color: Get.find<AppController>()
                                .getDarkBgTextFieldEnableBorderColorDefault(),
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderSide:
                              BorderSide(color: AppColors.appPrimaryColor),
                        ),
                      ),
                      style: TextStyle(color: AppColors.appBlackColor),
                    ),
                    SizedBox(
                      height: 12.h,
                    ),
                    Text(
                      storedLanguage['Confirm Passworde'] ?? "Confirm Password",
                      style: TextStyle(
                          fontSize: 16.sp,
                          color: AppColors.appBlack50,
                          fontWeight: FontWeight.w400),
                    ),
                    SizedBox(
                      height: 4.h,
                    ),
                    TextFormField(
                      validator: (value) {
                        if (value!.isEmpty) {
                          return 'Confirm password is required';
                        }
                        if (value != userController.password.text) {
                          return 'Your password and confirm password didn\'t match';
                        } else if (value.length < 5) {
                          return 'The password must be at least 5 characters.';
                        }
                        return null; // Return null if the input is valid.
                      },
                      obscureText:
                          !userController.isConfirmPasswordVisible.value,
                      controller: userController.confirmPassword,
                      decoration: InputDecoration(
                        suffixIcon: GestureDetector(
                          onTap: () {
                            userController.toggleConfirmPasswordVisibility();
                          },
                          child: Icon(
                            userController.isConfirmPasswordVisible.value
                                ? Icons.visibility
                                : Icons.visibility_off,
                            color: AppColors.appBlack70,
                          ),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                            vertical: 0, horizontal: 16),
                        filled: true, // Fill the background with color
                        hintStyle: TextStyle(
                          color: AppColors.appBlack30,
                        ),
                        fillColor: Get.find<AppController>()
                            .getDarkBgTextFieldColorDefault(), // Background color
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                            color: Get.find<AppController>()
                                .getDarkBgTextFieldEnableBorderColorDefault(),
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderSide:
                              BorderSide(color: AppColors.appPrimaryColor),
                        ),
                      ),
                      style: TextStyle(color: AppColors.appBlackColor),
                    ),
                    SizedBox(
                      height: 24.h,
                    ),
                    GestureDetector(
                      onTap: () {
                        if (_formKey.currentState!.validate()) {
                          userController.changePasswordRequest(
                              userController.currentPassword.text.toString(),
                              userController.password.text.toString(),
                              userController.confirmPassword.text.toString(),
                              context);
                        }
                      },
                      child: Container(
                        height: 45.h,
                        width: 326.w,
                        decoration: BoxDecoration(
                            color: AppColors.appPrimaryColor,
                            borderRadius: BorderRadius.circular(4)),
                        child: Center(
                          child: userController.isLoadingPassword == false
                              ? Text(
                                  storedLanguage['Update Password'] ??
                                      "Update Password",
                                  style: TextStyle(
                                      fontSize: 18.sp,
                                      color: AppColors.appWhiteColor,
                                      fontWeight: FontWeight.w500),
                                )
                              : CircularProgressIndicator(
                                  color: AppColors.appWhiteColor,
                                ),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 24.h,
                    ),
                  ],
                ),
              ),
            )
          ],
        ),
      );
    });
  }
}
