# Keep Stripe classes
-keep class com.stripe.android.** { *; }
-keep class com.stripe.android.pushProvisioning.** { *; }
-keep class com.stripe.android.pushProvisioning.PushProvisioningActivity { *; }
-keep class com.stripe.android.pushProvisioning.PushProvisioningActivityStarter { *; }
-keep class com.stripe.android.pushProvisioning.PushProvisioningEphemeralKeyProvider { *; }

# Keep Razorpay classes
-keep class com.razorpay.** { *; }
-keep class proguard.annotation.** { *; }
-keep class org.slf4j.** { *; }
-keep class org.slf4j.impl.** { *; }

# Keep React Native classes
-keep class com.facebook.react.** { *; }
-keep class com.facebook.hermes.** { *; }

# Keep WebView classes
-keep class android.webkit.** { *; }
-keep class com.tencent.smtt.** { *; }

# Keep general Android classes
-keep class android.app.** { *; }
-keep class android.content.** { *; }
-keep class android.os.** { *; }
-keep class android.view.** { *; }
-keep class android.widget.** { *; }

# Keep Kotlin classes
-keep class kotlin.** { *; }
-keep class kotlin.Metadata { *; }
-keep class kotlinx.** { *; }

# Keep your app's classes
-keep class com.Nestblock.Nestblock.** { *; }

# Keep Flutter classes
-keep class io.flutter.** { *; }
-keep class io.flutter.plugins.** { *; }
-keep class io.flutter.view.** { *; }
-keep class io.flutter.plugin.** { *; }
-keep class io.flutter.plugin.editing.** { *; }
-keep class io.flutter.plugin.platform.** { *; }
-keep class io.flutter.embedding.** { *; }
-keep class io.flutter.embedding.engine.** { *; }
-keep class io.flutter.embedding.android.** { *; }

# Keep Dio classes
-keep class okhttp3.** { *; }
-keep class okio.** { *; }
-keep class okio.internal.** { *; }

# Keep Pusher classes
-keep class com.pusher.** { *; }

# Keep PDF classes
-keep class com.itextpdf.** { *; }

# Keep PayPal classes
-keep class com.paypal.** { *; }

# Keep Flutterwave classes
-keep class com.flutterwave.** { *; }

# Keep Monnify classes
-keep class com.monnify.** { *; }

# Keep Paytm classes
-keep class com.paytm.** { *; }

# Keep WebView classes
-keep class com.pichillilorenzo.flutter_inappwebview.** { *; }

# Keep Lottie classes
-keep class com.airbnb.lottie.** { *; }

# Keep GetX classes
-keep class com.get.** { *; }

# Keep SharedPreferences classes
-keep class android.preference.** { *; }

# Keep Image Picker classes
-keep class io.flutter.plugins.imagepicker.** { *; }

# Keep Permission Handler classes
-keep class com.baseflow.permissionhandler.** { *; }

# Keep Path Provider classes
-keep class io.flutter.plugins.pathprovider.** { *; }

# Keep Local Notifications classes
-keep class com.dexterous.flutterlocalnotifications.** { *; }

# Keep Get Storage classes
-keep class com.getstorage.** { *; }

# Keep Connectivity classes
-keep class io.flutter.plugins.connectivity.** { *; }

# Keep Logger classes
-keep class com.internetitem.logback.** { *; }

# Keep Play Core classes
-keep class com.google.android.play.core.** { *; }
-keep class com.google.android.play.core.splitinstall.** { *; }
-keep class com.google.android.play.core.tasks.** { *; }
-keep class com.google.android.play.core.ktx.** { *; }

# Keep SLF4J classes
-keep class org.slf4j.** { *; }
-keep class org.slf4j.impl.** { *; }
-keep class org.slf4j.spi.** { *; }
-keep class org.slf4j.helpers.** { *; }

# Stripe
-dontwarn com.stripe.android.pushProvisioning.PushProvisioningActivity$g
-dontwarn com.stripe.android.pushProvisioning.PushProvisioningActivityStarter$Args
-dontwarn com.stripe.android.pushProvisioning.PushProvisioningActivityStarter$Error
-dontwarn com.stripe.android.pushProvisioning.PushProvisioningActivityStarter
-dontwarn com.stripe.android.pushProvisioning.PushProvisioningEphemeralKeyProvider

# General
-keepattributes *Annotation*
-keepattributes SourceFile,LineNumberTable
-keepattributes Signature
-keepattributes Exceptions
-keepattributes InnerClasses
-keepattributes EnclosingMethod
-keepattributes LocalVariable*Table
-keepattributes LocalVariableTypeTable
-keepattributes Deprecated
-keepattributes RuntimeVisible*Annotations
-keepattributes RuntimeInvisible*Annotations
-keepattributes RuntimeVisible*ParameterAnnotations
-keepattributes RuntimeInvisible*ParameterAnnotations