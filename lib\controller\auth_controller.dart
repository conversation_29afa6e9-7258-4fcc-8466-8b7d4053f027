import 'dart:async';

import 'package:Nestblock/routes/page_index.dart';
import 'package:country_code_picker/country_code_picker.dart';
import 'package:flutter/foundation.dart';
import '../data/datasource/remote/dio/dio_client.dart';
import '../data/model/base_model/api_provider.dart';
import '../data/model/base_model/api_response.dart';
import '../data/model/base_model/api_response.dart' as res;
import '../data/repository/auth_repo.dart';
import '../di_controller_index.dart';
import '../utils/app_colors.dart';
import '../utils/app_constants.dart';
import '../utils/helpers.dart';
import '../view/screens/auth/enetr_code.dart';
import 'package:dio/src/response.dart' as res;

import '../view/screens/auth/new_password.dart';
import '../view/screens/auth/signin_screen.dart';
import '../view/screens/landing/bottom_navbar.dart';

class AuthController extends GetxController {
  DioClient dioClient;
  final AuthRepo authRepo;
  AuthController({required this.authRepo, required this.dioClient});

  bool isLoadingLogin = false;
  bool isLoadingRegister = false;

  RxBool isLoadingForgetPassword = false.obs;

  bool isLoadingCode = false;

  bool isLoadingSubmit = false;

  Rx<String> selectedCountryName = ''.obs;
  Rx<String> selectedCountryNameCode = ''.obs;

  var rememberMe = false.obs;

  Rx<CountryCode> selectedCountryCode = CountryCode().obs;

  var isLoginPasswordVisible = false.obs;
  var isRegisterPasswordVisible = false.obs;
  var isRegisterConfirmPasswordVisible = false.obs;

  void toggleLoginPasswordVisibility() {
    isLoginPasswordVisible.value = !isLoginPasswordVisible.value;
    update();
  }

  void toggleRegisterPasswordVisibility() {
    isRegisterPasswordVisible.value = !isRegisterPasswordVisible.value;
    update();
  }

  void toggleRegisterConfirmPasswordVisibility() {
    isRegisterConfirmPasswordVisible.value =
        !isRegisterConfirmPasswordVisible.value;
    update();
  }

  @override
  void onInit() {
    // Set initial country code if needed
    selectedCountryCode.value = CountryCode.fromCode('US');
    super.onInit();
  }

  final firstNameTxt = TextEditingController();
  final lastNameTxt = TextEditingController();
  final userNameTxt = TextEditingController();
  final emailAddressTxt = TextEditingController();
  final phoneTxt = TextEditingController();
  final passwordTxt = TextEditingController();
  final confirmPasswordTxt = TextEditingController();

  final loginUserNameTxt = TextEditingController();
  final loginPasswordTxt = TextEditingController();

  final forgetPasswordTxt = TextEditingController();

  var firstNameValid = ''.obs;
  var lastNameValid = ''.obs;
  var userNameValid = ''.obs;
  var emailValid = ''.obs;
  var phoneValid = ''.obs;
  var signupPassValid = ''.obs;
  var signupConfirmPassValid = ''.obs;

  var signinUserNameValid = ''.obs;
  var signinPassword = ''.obs;

  RxBool isHideSignInPass = true.obs;
  RxBool isHideSignUpPass = true.obs;
  RxBool isHideSignUpConfirmPass = true.obs;

  signinValidation(context) {
    if (signinUserNameValid.value.isEmpty) {
      Helpers.showToast(msg: "UserName or Email is required.");
    } else if (signinPassword.value.isEmpty) {
      Helpers.showToast(msg: "Password is required.");
    } else {
      login(context, loginUserNameTxt.text.trim().toString(),
          loginPasswordTxt.text.trim().toString());
    }
  }

  signupValidation(BuildContext context) {
    if (firstNameValid.value.isEmpty) {
      Helpers.showToast(msg: "First Name is required.");
    } else if (lastNameValid.value.isEmpty) {
      Helpers.showToast(msg: "Last Name is required.");
    } else if (userNameValid.value.isEmpty) {
      Helpers.showToast(msg: "UserName is required.");
    } else if (phoneValid.value.isEmpty) {
      Helpers.showToast(msg: "Phone Number is required.");
    } else if (emailValid.value.isEmpty) {
      Helpers.showToast(msg: "Email Address is required.");
    } else if (signupPassValid.value.isEmpty) {
      Helpers.showToast(msg: "Password is required.");
    } else if (signupConfirmPassValid.value.isEmpty) {
      Helpers.showToast(msg: "Confirm Password is required.");
    } else {
      if (userNameValid.value.length < 5) {
        Helpers.showToast(msg: "The username must be at least 5 characters.");
      } else if (!emailValid.value.contains('@')) {
        Helpers.showToast(
            msg: "'@' Keyword should be included in your email address.");
      } else if (signupPassValid.value != signupConfirmPassValid.value) {
        Helpers.showToast(
            msg: "Your Password and Confirm Password didn't match.");
      } else if (signupPassValid.value.length < 6 &&
          signupConfirmPassValid.value.length < 6) {
        Helpers.showToast(
            msg: "Your Password should be greater than 6 characters.");
      } else {
        register(
            context,
            firstNameTxt.text.trim().toString(),
            lastNameTxt.text.trim().toString(),
            userNameTxt.text.trim().toString(),
            emailAddressTxt.text.trim().toString(),
            phoneTxt.text.trim().toString().startsWith('09')
                ? {
                    selectedCountryCode.value.dialCode! +
                        phoneTxt.text.trim().toString().substring(1)
                  }
                : {
                    selectedCountryCode.value.dialCode! +
                        passwordTxt.text.trim().toString()
                  },
            passwordTxt.text.trim().toString(),
            confirmPasswordTxt.text.trim().toString());
      }
    }
  }

  Future<dynamic> register(
    BuildContext context,
    dynamic firstName,
    dynamic lastName,
    dynamic userName,
    dynamic emailAddress,
    dynamic phone,
    dynamic password,
    dynamic confirmPassword,
  ) async {
    isLoadingRegister = true;
    update();
    res.Response? response;

    try {
      response = await ApiProvider.postData(
          ENDPOINT_URL: AppConstants.registerUri,
          data: {
            "firstname": firstName,
            "lastname": lastName,
            "username": userName,
            "email": emailAddress,
            "phone": phone,
            "password": password,
            "password_confirmation": confirmPassword,
          });
      if (response.statusCode == 200) {
        isLoadingRegister = false;
        Map map = response.data;

        dynamic token;
        dynamic msg;
        dynamic status;

        update();

        try {
          token = map["data"]["token"];
          msg = map["message"];
          status = map["status"];

          update();
          if (kDebugMode) {
            print(token);
          }
          final backgroundColor = status != true ? Colors.red : Colors.green;

          final snackBar = SnackBar(
            content: Text(
              msg,
              style: TextStyle(color: Colors.white, fontSize: 14.sp),
            ),
            backgroundColor: backgroundColor,
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            margin: const EdgeInsets.all(10),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            elevation: 10,
          );

          ScaffoldMessenger.of(context).showSnackBar(snackBar);

          if (token.isNotEmpty) {
            Get.offNamedUntil(BottomNavBar.routeName, (route) => false);
            authRepo.saveUserToken(token);
            // await authRepo.updateToken();
          }
        } catch (e) {
          if (kDebugMode) {
            print("e");
          }
        }
        if (token.isNotEmpty) {
          authRepo.saveUserToken(token);
          // await authRepo.updateToken();
        }
        // callback(true, token, temporaryToken, message);
        update();
      } else {
        isLoadingRegister = false;
        // print('ApiResponse is: ' + apiResponse.error);
        update();
      }
      // return apiResponse.response!.statusCode;
    } on DioError catch (e) {
      isLoadingRegister = false;
      update();
      List<dynamic> errorList = e.response!.data['errors'];
      // Format error messages with numbering
      List<String> formattedErrors = errorList.asMap().entries.map((entry) {
        int index = entry.key + 1;
        String errorMessage = entry.value.toString();
        return "$index. $errorMessage";
      }).toList();

      // Helpers().showValidationErrorDialog(
      //     title: "Error", errorText: formattedErrors.join('\n'));
      Helpers.showSnackBar(msg: formattedErrors.join('\n'));
    }
    // ApiResponse apiResponse = await authRepo.register(context, firstName,
    //     lastName, userName, emailAddress, phone, password, confirmPassword);
  }

  Future<dynamic> login(
      BuildContext context, dynamic userName, dynamic password) async {
    isLoadingLogin = true;
    update();
    res.ApiResponse apiResponse =
        await authRepo.login(context, userName, password);

    if (apiResponse.response != null &&
        apiResponse.response!.statusCode == 200) {
      isLoadingLogin = false;
      Map map = apiResponse.response!.data;

      dynamic token;
      dynamic msg;
      dynamic status;

      if (map['status'] == true) {
        try {
          token = map["data"]["token"];
          msg = map["message"];
          status = map["status"];
          if (kDebugMode) {
            // print('map data: ' + map.toString());
          }

          final snackBar = SnackBar(
            content: Text(
              msg,
              style: TextStyle(color: Colors.white, fontSize: 14.sp),
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            margin: const EdgeInsets.all(5),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            elevation: 10,
          );

          ScaffoldMessenger.of(context).showSnackBar(snackBar);

          if (token.isNotEmpty) {
            authRepo.saveUserToken(token);
            Get.offNamedUntil(BottomNavBar.routeName, (route) => false);
            // await authRepo.updateToken();
          }
        } catch (e) {
          if (kDebugMode) {
            print("error is occoured!");
          }
        }
      } else {
        final snackBar = SnackBar(
          content: Text(
            map['message'],
            style: TextStyle(color: Colors.white, fontSize: 14.sp),
          ),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.all(5),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          elevation: 10,
        );

        ScaffoldMessenger.of(context).showSnackBar(snackBar);
      }

      update();
    } else {
      isLoadingLogin = false;
      update();
    }
    return apiResponse.response!.statusCode;
  }

  dynamic emailForgetPassword;

  late Timer timer;
  Rx<int> remainingTime = 300.obs; // 5 minutes (5 * 60 seconds)
  bool isTimerRunning = false;

  void startTimer() {
    if (isTimerRunning) {
      return; // If the timer is already running, don't start a new one
    }
    timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (remainingTime > 0) {
        remainingTime.value--;
      } else {
        // Timer has reached zero, you can perform any action here if needed
        timer.cancel(); // Cancel the timer
        isTimerRunning = false; // Set the timer running flag to false
      }
      update();
    });
  }

  // Function to reset the timer
  void resetTimer() {
    update();
    if (kDebugMode) {
      print("Time reset");
    }
    remainingTime.value = 300; // Reset the remaining time to its initial value
    isTimerRunning = false; // Set the timer running flag to false
  }

  var forgotPassEmailTextEditingCtrlr = TextEditingController();
  Future<dynamic> forgetPasswordRequest(
    BuildContext context,
    dynamic email,
  ) async {
    isLoadingForgetPassword.value = true;
    update();
    res.ApiResponse apiResponse = await authRepo.forgetPasswordRequest(email);

    if (apiResponse.response != null &&
        apiResponse.response!.statusCode == 200) {
      isLoadingForgetPassword.value = false;
      update();
      if (apiResponse.response!.data != null) {
        var msg = apiResponse.response!.data['message'];

        if (msg is Map) {
          Helpers().showValidationErrorDialog(
              bgColor: apiResponse.response!.data['status'] == false
                  ? AppColors.appRedColor
                  : AppColors.appGreenColor,
              title: apiResponse.response!.data['status'] == false
                  ? "Error"
                  : "Success",
              messageText: Text(
                msg['email'][0],
                style: TextStyle(color: Colors.white),
              ));
        }
        if (msg is String) {
          Helpers().showValidationErrorDialog(
              bgColor: apiResponse.response!.data['status'] == false
                  ? AppColors.appRedColor
                  : AppColors.appGreenColor,
              title: apiResponse.response!.data['status'] == false
                  ? "Error"
                  : "Success",
              messageText: Text(
                msg,
                style: TextStyle(color: Colors.white),
              ));
        }

        if (msg == "Mail send successfully") {
          Helpers().showValidationErrorDialog(
              bgColor: apiResponse.response!.data['status'] == false
                  ? AppColors.appRedColor
                  : AppColors.appGreenColor,
              title: apiResponse.response!.data['status'] == false
                  ? "Error"
                  : "Success",
              messageText: Text(
                msg,
                style: TextStyle(color: Colors.white),
              ));
          Get.bottomSheet(
            Container(
              height: MediaQuery.of(context).size.height * 0.4,
              width: double.infinity,
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                  color: AppColors.appWhiteColor,
                  borderRadius:
                      BorderRadius.vertical(top: Radius.circular(20.r))),
              child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: 16.w,
                    vertical: 10.h,
                  ),
                  child: EnterCode()),
            ),
          );
        }

        update();
      }
    } else {
      isLoadingForgetPassword.value = false;
      update();
    }
  }

  var otpTextEditingController = TextEditingController();
  Future<dynamic> recoveryPassCodeRequest(
    BuildContext context,
    dynamic email,
    dynamic code,
  ) async {
    isLoadingCode = true;
    update();
    ApiResponse apiResponse =
        await authRepo.recoveryPassCodeRequest(context, email, code);

    if (apiResponse.response != null &&
        apiResponse.response!.statusCode == 200) {
      isLoadingCode = false;
      update();
      if (apiResponse.response!.data != null) {
        dynamic status = apiResponse.response!.data['status'];
        dynamic msg = apiResponse.response!.data['message'];

        if (status == true) {
          Get.back();
          Get.bottomSheet(
            Container(
              color: AppColors.appWhiteColor,
              height: MediaQuery.of(context).size.height * 0.6,
              width: double.infinity,
              padding: const EdgeInsets.all(10),
              child: Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: 16.w,
                  vertical: 10.h,
                ),
                child: NewPassword(),
              ),
            ),
          );
          startTimer();
        } else {
          Get.snackbar(
            'Message',
            '$msg',
            backgroundColor: Colors.red,
            colorText: Colors.white,
            duration: const Duration(seconds: 2),
            snackPosition: SnackPosition.BOTTOM,
            margin: const EdgeInsets.all(10),
            borderRadius: 8,
            shouldIconPulse: true,
            icon: const Icon(Icons.cancel, color: Colors.white),
            barBlur: 10,
          );
        }
        update();
      }
    } else {
      isLoadingCode = false;
      update();
    }
  }

  var newPassTextEdittingController = TextEditingController();
  var confirmPassTextEdittingController = TextEditingController();
  Future<dynamic> forgetPassSubmitRequest(
    BuildContext context,
    dynamic pass,
    dynamic passConfirm,
    dynamic email,
  ) async {
    isLoadingSubmit = true;
    update();
    ApiResponse apiResponse = await authRepo.forgetPasswordSubmitRequest(
        context, pass, passConfirm, email);

    if (apiResponse.response != null &&
        apiResponse.response!.statusCode == 200) {
      isLoadingSubmit = false;
      update();
      if (apiResponse.response!.data != null) {
        dynamic status = apiResponse.response!.data['status'];
        dynamic msg = apiResponse.response!.data['message'];

        if (status == true) {
          Get.offAllNamed(SignInScreen.routeName);
          Get.snackbar(
            'Message',
            '$msg',
            backgroundColor: Colors.green,
            colorText: Colors.white,
            duration: const Duration(seconds: 2),
            snackPosition: SnackPosition.BOTTOM,
            margin: const EdgeInsets.all(10),
            borderRadius: 8,
            barBlur: 10,
          );
        } else {
          Get.snackbar(
            'Message',
            '$msg',
            backgroundColor: Colors.red,
            colorText: Colors.white,
            duration: const Duration(seconds: 2),
            snackPosition: SnackPosition.BOTTOM,
            margin: const EdgeInsets.all(10),
            borderRadius: 8,
            shouldIconPulse: true,
            icon: const Icon(Icons.cancel, color: Colors.white),
            barBlur: 10,
          );
        }
        update();
      }
    } else {
      isLoadingSubmit = false;
      update();
    }
  }

  // for user Section
  dynamic getUserToken() {
    update();
    if (kDebugMode) {
      print(authRepo.getUserToken());
    }
    return authRepo.getUserToken();
  }

  // remove user Section
  void removeUserToken() {
    update();
    if (kDebugMode) {
      print("remove");
      print(authRepo.removeUserToken());
    }
    authRepo.removeUserToken();
  }

  //get auth token
  // for user Section
  String getAuthToken() {
    update();
    dioClient.updateHeader(authRepo.getAuthToken(), '');
    return authRepo.getAuthToken();
  }
}
