import 'dart:convert';
import 'dart:io';
import 'package:Nestblock/di_controller_index.dart';
import 'package:Nestblock/routes/page_index.dart';
import 'package:flutter/foundation.dart';
// import 'package:flutter_paystack/flutter_paystack.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:flutterwave_standard_smart/flutterwave.dart';
import 'package:monnify_payment_sdk/monnify_payment_sdk.dart';
// import 'package:paytm/paytm.dart';
// import 'package:paytmpayments_allinonesdk/paytmpayments_allinonesdk.dart';
import '../data/model/base_model/api_response.dart';
import '../data/model/response/payment_gateway_model.dart';
import '../utils/helpers.dart';
import '../view/screens/landing/payment/ap_payment_fail.dart';
import '../view/screens/landing/payment/app_payment_success.dart';
import '../view/screens/landing/payment/pay_now_webview_screen.dart';
import '../view/screens/success/success_screen.dart';
import '../view/widgets/app_bottom_sheet.dart';
import 'package:flutter_paypal_payment/flutter_paypal_payment.dart';

class PaymentGatewayController extends GetxController {
  static PaymentGatewayController get to =>
      Get.find<PaymentGatewayController>();
  final PaymentGatewayRepo paymentGatewayRepo;

  PaymentGatewayController({required this.paymentGatewayRepo});

  @override
  // void onInit() {
  // getPaymentGatewayList();
  // listenRazorPay();
  // initMonnify();
  // initPayStack();
  //   super.onInit();
  // }
  @override
  void dispose() {
    // razorpay!.clear();
    super.dispose();
  }

  var amountTextEditingCtrlr = TextEditingController();
  var selectedValue;
  String selectedImage = "";

  // payment calculation====================
  //======from payment_gateways api
  String parcentageCharge = "";
  String fixedCharge = "";
  String conversionRate = "";
  String gatewayId = "";
  String gatewayCode = "";
  //======from money_calculation_proceed api
  String totalBaseAmountPay = "";
  String sendCurrency = "";

  // calculation
  //====this value will be displayed in the payment method page
  String totalPayable = "";
  String charge_amount = "";
  calculate() {
    totalBaseAmountPay = totalBaseAmountPay.replaceAll(',', '');
    var getParcentageCharge =
        (double.parse(parcentageCharge) * double.parse(totalBaseAmountPay)) /
            100;
    var getfixedCharge = getParcentageCharge + double.parse(fixedCharge);
    charge_amount = (getParcentageCharge + getfixedCharge).toString();
    totalPayable =
        (double.parse(charge_amount) + double.parse(totalBaseAmountPay))
            .toStringAsFixed(2);
  }

  int selectedGatewayType = 0;
  void onChanged(value) {
    // selected value
    selectedValue = value.name;
    sendCurrency = value.currency!;
    // get the selected list and filter it
    var selectedGatewayElement =
        gatewayList.where((e) => e.name == value.name).toList();
    for (var i in selectedGatewayElement) {
      selectedImage = i.image ?? "";
      parcentageCharge = i.percentageCharge ?? "";
      fixedCharge = i.fixedCharge ?? "";
      print(
        "=========================PARCENTAGE CHARGE: ${i.percentageCharge}",
      );
      print("=========================FIXED CHARGE: ${i.fixedCharge}");
      print("=========================CONVERSION RATE: ${i.conventionRate}");
      print("=========================SELECTED IMAGE: ${i.image}");
      conversionRate = i.conventionRate ?? "";
      gatewayId = i.id.toString();
      gatewayCode = i.code ?? "";

      // check which type of gateway is selected
      //---check manual payment
      // 1 = manual payement; 2 = other payment; 3 = card payment
      if (i.id! > 999) {
        selectedGatewayType = 1;
      } else if (i.code! == "securionpay" || i.code == "authorizenet") {
        selectedGatewayType = 3;
      } else {
        selectedGatewayType = 2;
      }
    }
    // calculate the amount
    calculate();
    update();
  }

  // on paynow clicked
  List<ManualPaymentDynamicFormModel> selectedManualPaymentList = [];
  String invoiceNumber = "";
  bool isLoadingPaymentSheet = false;
  onPayNowTapped(context) async {
    if (selectedGatewayType == 0) {
      Helpers.showSnackBar(msg: "Please select a Payment Gateway first.");
    } else if (selectedGatewayType == 1) {
      selectedManualPaymentList = manualPaymentElementList
          .where((e) => e.gatewayName == selectedValue)
          .toList();

      Get.toNamed(DepositPreviewScreen.routeName);
    } else if (selectedGatewayType == 2) {
      if (gatewayCode == "stripe") {
        isLoadingPaymentSheet = true;
        update();
        await stripeDepositRequest();
        isLoadingPaymentSheet = false;
        update();
      } else if (gatewayCode == "flutterwave") {
        isLoadingPaymentSheet = true;
        update();
        await handleFlutterwavePaymentInitialization(context);
        isLoadingPaymentSheet = false;
        update();
      } else if (gatewayCode == "paytm") {
        isLoadingPaymentSheet = true;
        update();
        makePaytmPayment(); // 0 = testing mode
        isLoadingPaymentSheet = false;
        update();
      } else if (gatewayCode == "monnify") {
        isLoadingPaymentSheet = true;
        update();
        await makeMonnifyPayment();
        isLoadingPaymentSheet = false;
        update();
      }
      // else if (gatewayCode == "paystack") {
      //   await payStackchargeCard(context);
      // }
      else if (gatewayCode == "paypal") {
        makePaypalPaymentRequest();
      }
      //  else if (gatewayCode == "razorpay") {
      //   isLoadingPaymentSheet = true;
      //   update();
      //   await razorPayPaymentRequest();
      //   isLoadingPaymentSheet = false;
      //   update();
      // }
      else {
        await sendOtherPaymentRequest(
          Get.find<MoneyCalculationProceedController>().invoiceNumber.value,
          gatewayId,
        );
      }
    } else if (selectedGatewayType == 3) {
      Get.to(() => CardPaymentScreen(gatewayCode: gatewayCode));
    }
    update();
  }

  bool _isLoading = false;
  bool get isLoading => _isLoading;
  dynamic res;

  /// get all payment gateway list
  List<Gateway> gatewayList = [];
  List<ManualPaymentDynamicFormModel> manualPaymentElementList = [];

  Future<dynamic> getPaymentGatewayList() async {
    _isLoading = true;
    update();
    ApiResponse apiResponse = await paymentGatewayRepo.getPaymentMethodList();

    if (apiResponse.response != null &&
        apiResponse.response!.statusCode == 200) {
      _isLoading = false;
      gatewayList = [];
      res = apiResponse.response!.data;
      update();
      if (res != null) {
        if (res['status'] == false) {
          _isLoading = false;
          Get.find<AuthController>().removeUserToken();
        }
        if (res['message'] == "Email Verification Required") {
          _isLoading = false;
          Get.find<AuthController>().removeUserToken();
        } else if (res['message'] == "Mobile Verification Required") {
          _isLoading = false;
          Get.find<AuthController>().removeUserToken();
        } else if (res['message'] == "Two FA Verification Required") {
          _isLoading = false;
          Helpers.showSnackBar(msg: res['message']);
          Get.offAll(TwoFaScreen(isTwofaVerification: true));
        } else if (res['message'] == "Your account has been suspend") {
          _isLoading = false;
          Helpers.showSnackBar(msg: res['message']);
          Get.find<AuthController>().removeUserToken();
          Get.offAllNamed(SignInScreen.routeName);
        } else if (res['message'] == "Identity Verification Required") {
          _isLoading = false;
          Helpers.showSnackBar(msg: res['message']);
          Get.offAll(ProfileSettingScreen(isIdentityVerification: true));
        } else if (res['message'] == "Address Verification Required") {
          _isLoading = false;
          Helpers.showSnackBar(msg: res['message']);
          Get.offAll(ProfileSettingScreen(isAddressVerification: true));
        } else {
          gatewayList.addAll(PaymentGatewayModel.fromJson(res).data!.gateways!);

          // filter manual payment list
          manualPaymentElementList = [];
          List allList = res['data']['gateways'];
          var manualList = allList.where((e) => e['id'] > 999).toList();
          getNeededGatewayKeys(allList);

          // filter the dynamic filed data
          Map fieldList = {};
          for (var i in manualList) {
            fieldList.addAll(i['parameters']);
            fieldList.forEach((key, value) {
              manualPaymentElementList.add(
                ManualPaymentDynamicFormModel(
                  gatewayName: i['name'],
                  fieldName: value['field_name'],
                  fieldLevel: value['field_level'],
                  type: value['type'],
                  validation: value['validation'],
                ),
              );
            });
          }

          update();
        }
      }
    } else {
      _isLoading = false;
      update();
    }
  }

  // post manual payment
  bool isFormsubmitting = false;
  Future<dynamic> makeManualPayment({
    required Map<String, dynamic> body,
    required BuildContext context,
  }) async {
    isFormsubmitting = true;
    update();
    ApiResponse apiResponse = await paymentGatewayRepo.manualPayment(
      data: body,
    );

    if (apiResponse.response != null &&
        apiResponse.response!.statusCode == 200) {
      isFormsubmitting = false;
      update();
      if (apiResponse.response!.data != null) {
        Map<String, dynamic> response = apiResponse.response!.data;
        print(response);
        bool hasError = response.containsKey('errors');
        if (hasError) {
          Map errorData = apiResponse.response!.data['errors'];
          errorData.forEach((field, errors) {
            final errorMessages = errors
                .map(
                  (error) => Center(
                    child: Text(
                      error,
                      style: TextStyle(
                        color: AppColors.appRedColor,
                        fontFamily: "Dubai",
                      ),
                    ),
                  ),
                )
                .toList();
            final snackBar = SnackBar(
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [...errorMessages],
              ),
            );
            ScaffoldMessenger.of(context).showSnackBar(snackBar);
          });
        } else {
          final snackBar = SnackBar(
            backgroundColor: AppColors.appGreenColor,
            content: Center(
              child: Text(
                response['status'] == true
                    ? response['data']
                    : response['message'],
                style: TextStyle(
                  color: AppColors.appWhiteColor,
                  fontFamily: "Dubai",
                ),
              ),
            ),
          );
          ScaffoldMessenger.of(context).showSnackBar(snackBar);
          if (response['status'] == true) {
            onPaymentDone(
              Get.find<MoneyCalculationProceedController>().invoiceNumber.value,
              gatewayId,
            );
            Get.offAll(() => ThankYouPage());
          }
        }

        update();
      }
    } else {
      isFormsubmitting = false;
      update();
    }
  }

  // post card payment
  Future<dynamic> cardPaymentRequest(
    dynamic invoiceNumber,
    dynamic gatewayId,
    dynamic cardNumber,
    dynamic cardName,
    dynamic expiryMonth,
    dynamic expiryYear,
    dynamic cardCVC,
    BuildContext context,
  ) async {
    isFormsubmitting = true;
    update();
    ApiResponse apiResponse = await paymentGatewayRepo.cardPaymentRequest(
      invoiceNumber,
      gatewayId,
      cardNumber,
      cardName,
      expiryMonth,
      expiryYear,
      cardCVC,
    );

    if (apiResponse.response != null &&
        apiResponse.response!.statusCode == 200) {
      isFormsubmitting = false;
      update();
      if (apiResponse.response!.data != null) {
        var res = apiResponse.response!.data;
        if (res["message"] == "Email Verification Required") {
          Helpers.showSnackBar(msg: res['message']);
          await appBottomSheet();
        } else if (res["message"] == "Mobile Verification Required") {
          Helpers.showSnackBar(msg: res['message']);
          await appBottomSheet(isMailVerification: false);
        } else if (res["message"] == "Two FA Verification Required") {
          Get.offAllNamed(TwoFaScreen.routeName);
          Helpers.showSnackBar(msg: res['message']);
        } else if (res["message"] == "Your account has been suspend") {
          Get.find<AuthController>().removeUserToken();
          await Get.offNamedUntil(SignInScreen.routeName, (route) => false);
        } else {
          dynamic status = apiResponse.response!.data['status'];
          dynamic msg = apiResponse.response!.data['message'];

          if (status == true) {
            onPaymentDone(invoiceNumber, gatewayId);

            Get.offAll(() => ThankYouPage());
            Helpers.showSnackBar(msg: msg, bgColor: AppColors.appGreenColor);
          } else {
            Helpers.showSnackBar(msg: msg, bgColor: AppColors.appRedColor);
          }

          update();
        }
      }
    } else {
      isFormsubmitting = false;
      update();
    }
  }

  // post other payment
  dynamic url;
  Future<dynamic> sendOtherPaymentRequest(
    dynamic invoiceNumber,
    dynamic gatewayId,
  ) async {
    isFormsubmitting = true;
    update();
    ApiResponse apiResponse = await paymentGatewayRepo.sendOtherPaymentRequest(
      invoiceNumber,
      gatewayId,
    );

    if (apiResponse.response != null &&
        apiResponse.response!.statusCode == 200) {
      isFormsubmitting = false;
      update();
      if (apiResponse.response!.data != null) {
        Map map = apiResponse.response!.data;
        if (map['status'] == true) {
          url = map["data"]["url"];
          if (url != null) {
            Get.to(() => CheckoutWebView(url: url));
          }
        } else {
          Helpers.showSnackBar(msg: map['message']);
        }

        update();
      }
    } else {
      isFormsubmitting = false;
      update();
    }
  }

  // on payment done
  Future<dynamic> onPaymentDone(
    dynamic invoiceNumber,
    dynamic gatewayId,
  ) async {
    update();
    ApiResponse apiResponse = await paymentGatewayRepo.onPaymemtDone(
      invoiceNumber,
      gatewayId,
    );

    if (apiResponse.response != null &&
        apiResponse.response!.statusCode == 200) {
      update();
      if (apiResponse.response!.data != null) {
        print('On Payment done res:  ${apiResponse.response!.data}');
      } else {
        update();
      }
    }
  }

  ///-------------------------All key of Payment Gateway--------------------
  getNeededGatewayKeys(List<dynamic> allList) {
    for (var i in allList) {
      if (i['code'] == 'stripe') {
        secretKeyStripe = i['parameters']['secret_key'];
        publishableKeyStripe = i['parameters']['publishable_key'];
      } else if (i['code'] == 'razorpay') {
        razorPayKey = i['parameters']['key_id'];
      } else if (i['code'] == 'monnify') {
        monnifyApiKey = i['parameters']['api_key'];
        monnifyContractCode = i['parameters']['contract_code'];
      } else if (i['code'] == 'paytm') {
        paytmMid = i['parameters']['MID'];
        paytmMarcentKey = i['parameters']['merchant_key'];
        paytmWebsite = i['parameters']['WEBSITE'];
      } else if (i['code'] == 'paystack') {
        payStackpublicKey = i['parameters']['public_key'];
      } else if (i['code'] == 'paypal') {
        paypalClientId = i['parameters']['cleint_id'];
        paypalSecretKey = i['parameters']['secret'];
      } else if (i['code'] == 'flutterwave') {
        flutterwavePublicKey = i['parameters']['public_key'];
      }
    }
  }

  // STRIPE
  String secretKeyStripe =
      "sk_test_51R1lpx4cQ7QgUw06zekTxDgQuzEfwQYNoH4FaAI9yzPVuwDYS0vzDmqnvvsVDF1TGEpqDy7prIC36JsWeO2jp42r00BlcSx5EY";
  String publishableKeyStripe =
      "pk_test_51R1lpx4cQ7QgUw06rdMGwX4KRSFQCje4WMEp0Ld82WWMcDx3f4GVcpPiX0olxstgHSbVtRXuIYdvuW4FbrsyYDHB00Qg5v7Sud";
  // RAZORPAY
  String razorPayKey = "";
  // MONNIFY
  String monnifyApiKey = '';
  String monnifyContractCode = '';
  // PAYTM
  String paytmMid = "";
  String paytmMarcentKey = "";
  String paytmWebsite = "";
  // PAYSTACK
  String payStackpublicKey = '';
  // PAYPAL
  String paypalClientId = "";
  String paypalSecretKey = "";
  // FLUTTERWAVE
  String flutterwavePublicKey = "";

  ///-------------------------Stripe Payment Integration
  dynamic stripePaymentData;
  var stripe = Stripe.instance;

  Future<void> stripeDepositRequest() async {
    try {
      stripePaymentData = await stripePaymentCreate(
        calculateAmount(totalPayable),
        '$sendCurrency',
      );
      await stripe.initPaymentSheet(
        paymentSheetParameters: SetupPaymentSheetParameters(
          paymentIntentClientSecret: stripePaymentData['client_secret'],
          style: ThemeMode.dark,
          merchantDisplayName: 'Nestblock',
        ),
      );

      displayPaymentSheet();
    } catch (e, s) {
      if (kDebugMode) {
        print('Payment exception: $e$s');
      }
    }
  }

  Future displayPaymentSheet() async {
    try {
      await stripe.presentPaymentSheet().then((newValue) async {
        onPaymentDone(
          Get.put(
            MoneyCalculationProceedController(
              moneyCalculationProceedRepo: sl(),
            ),
          ).invoiceNumber.value,
          gatewayId,
        );
        Get.dialog(AppPaymentSuccess());

        Get.offAll(() => ThankYouPage());
        stripePaymentData = null;
      }).onError((error, stackTrace) async {
        if (kDebugMode) {
          print(
            'OnErrorException/DISPLAYPAYMENTSHEET==> $error $stackTrace',
          );
        }
        _isLoading = false;
        Get.dialog(
          AlertDialog(
            content: Container(
              height: 60.h,
              child: Center(
                child: Text(
                  "Payment Cancelled!",
                  style: TextStyle(color: AppColors.appRedColor),
                ),
              ),
            ),
          ),
        );
        update();
      });
    } on StripeException catch (e) {
      if (kDebugMode) {
        print('StripeException/DISPLAYPAYMENTSHEET==> $e');
      }
      Get.dialog(
        AlertDialog(
          content: Container(
            height: 60.h,
            child: Center(
              child: Text(
                "Payment Cancelled!",
                style: TextStyle(color: AppColors.appRedColor),
              ),
            ),
          ),
        ),
      );
      await Future.delayed(Duration(seconds: 3));
      Get.back();
    } catch (e) {
      if (kDebugMode) {
        print('$e');
      }
    }
  }

  stripePaymentCreate(String amount, String currency) async {
    try {
      var body = {
        'amount': amount,
        'currency': currency,
        'automatic_payment_methods': {'enabled': true},
      };

      var response = await Dio().post(
        'https://api.stripe.com/v1/payment_intents',
        data: body,
        options: Options(
          headers: {
            'Authorization': 'Bearer ' + "${secretKeyStripe}",
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        ),
      );

      // http.post(
      //   Uri.parse(),
      //   body: body,
      //   headers: {
      //     'Authorization': 'Bearer ' + "${secretKeyStripe}",
      //     'Content-Type': 'application/x-www-form-urlencoded',
      //   },
      // );
      return response.data;
    } catch (err) {
      if (kDebugMode) {
        print('err charging user: ${err.toString()}');
      }
      return {};
    }
  }

  // //calculate Amount
  calculateAmount(String amount) {
    final doubVal = double.parse(amount);
    final calculatedAmount = (doubVal.toInt() * 100);
    return calculatedAmount.toString();
  }

  ///----------------------Razor Payment Integration
  // Razorpay? razorpay;

  // listenRazorPay() {
  //   razorpay = Razorpay()
  //     ..on(Razorpay.EVENT_PAYMENT_SUCCESS, _handlePaymentSuccess)
  //     ..on(Razorpay.EVENT_PAYMENT_ERROR, _handlePaymentError)
  //     ..on(Razorpay.EVENT_EXTERNAL_WALLET, _handleExternalWallet);
  // }

  // void _handlePaymentSuccess(PaymentSuccessResponse response) async {
  //   print(response);
  //   onPaymentDone(
  //       Get.find<MoneyCalculationProceedController>().invoiceNumber.value,
  //       gatewayId);
  //   Get.to(() => ThankYouPage());
  //   Get.dialog(AppPaymentSuccess());
  // }

  // void _handlePaymentError(PaymentFailureResponse response) {
  //   print(response.message);
  //   print(response.error);
  //   // Handle payment failure
  //   Get.dialog(AppPaymentFail(errorText: response.message!));
  // }

  // void _handleExternalWallet(ExternalWalletResponse response) {
  //   // Handle external wallet payment
  // }

  // Future razorPayPaymentRequest() async {
  //   final options = {
  //     // Replace with your actual Razorpay key
  //     'key': razorPayKey,
  //     'amount': calculateAmount(totalPayable),
  //     'name': 'Test',
  //     'description': 'Test Payment',
  //     'prefill': {'contact': '1234567890', 'email': '<EMAIL>'},
  //     'external': {
  //       'wallets': ['paytm']
  //     },
  //     'currency': '$sendCurrency'
  //   };

  //   try {
  //     razorpay!.open(options);
  //   } catch (e) {
  //     if (kDebugMode) {
  //       print('Error: $e');
  //     }
  //   }
  // }

  ///-------------------------Monnify Payment Integration
  late Monnify? monnify;
  Future initMonnify() async {
    monnify = await Monnify.initialize(
      applicationMode: ApplicationMode.TEST,
      apiKey: monnifyApiKey,
      contractCode: monnifyContractCode,
    );
  }

  Future<void> makeMonnifyPayment() async {
    final paymentReference = DateTime.now().toIso8601String();

    final transaction = TransactionDetails().copyWith(
      amount: double.parse(totalPayable),
      currencyCode: '$sendCurrency',
      customerName: 'Customer Name',
      customerEmail: '<EMAIL>',
      paymentReference: paymentReference,
    );

    try {
      var res = await monnify?.initializePayment(transaction: transaction);
      if (res!.transactionStatus == 'SUCCESS') {
        onPaymentDone(
          Get.find<MoneyCalculationProceedController>().invoiceNumber.value,
          gatewayId,
        );
        Get.offAll(() => ThankYouPage());
      } else {
        Get.offAll(() => ThankYouPage());
        print("Make Monnify Payment: ${res}");
      }
    } catch (e) {
      // handle exceptions in here.
      print(e);
    }
  }

  ///-------------------------Paytm Payment Integration
  String paytmOrderId = "orderId" + DateTime.now().toIso8601String();
  String paytmTxnToken =
      "txnToken" + DateTime.now().millisecondsSinceEpoch.toString();
  bool isStaging = true; // true = testing; false = production

  bool restrictAppInvoke = false;
  Future makePaytmPayment() async {
    // try {
    //   var response = PaytmPaymentsAllinonesdk().startTransaction(
    //     paytmMid,
    //     paytmOrderId,
    //     totalPayable,
    //     paytmTxnToken,
    //     "",
    //     isStaging,
    //     restrictAppInvoke,
    //   );
    //   response.then((value) async {
    //     onPaymentDone(
    //       Get.find<MoneyCalculationProceedController>().invoiceNumber.value,
    //       gatewayId,
    //     );
    //     Get.offAll(() => ThankYouPage());
    //   }).catchError((onError) {
    //     if (onError is PlatformException) {
    //       if (kDebugMode) {
    //         print("OnError: " + onError.toString());
    //       }
    //     } else {
    //       print("Other error occurred!");
    //     }
    //   });
    // } catch (err) {
    //   if (kDebugMode) {
    //     print("Catch Error: " + err.toString());
    //   }
    // }
  }
  // void makePaytmPayment(int mode) async {
  //   String callBackUrl = (isStaging
  //           ? 'https://securegw-stage.paytm.in'
  //           : 'https://securegw.paytm.in') +
  //       '/theia/paytmCallback?ORDER_ID=' +
  //       paytmOrderId;

  //   //Host the Server Side Code on your Server and use your URL here. The following URL may or may not work. Because hosted on free server.
  //   //Server Side code url: https://github.com/mrdishant/Paytm-Plugin-Server
  //   var url = Uri.parse(
  //       'https://desolate-anchorage-29312.herokuapp.com/generateTxnToken');

  //   var body = json.encode({
  //     "mid": paytmMid,
  //     "key_secret": paytmMarcentKey,
  //     "website": paytmWebsite,
  //     "orderId": paytmOrderId,
  //     "amount": "$totalPayable",
  //     "callbackUrl": callBackUrl,
  //     "custId": "122",
  //     "mode": mode.toString(),
  //     "testing": isStaging ? 0 : 1
  //   });

  //   try {
  //     final response = await http.post(
  //       url,
  //       body: body,
  //       headers: {'Content-type': "application/json"},
  //     );
  //     print("Response is");
  //     print(response.body);

  //     var paytmResponse = Paytm.payWithPaytm(
  //         mId: paytmMid,
  //         orderId: paytmOrderId,
  //         txnToken: paytmTxnToken,
  //         txnAmount: totalPayable,
  //         callBackUrl: callBackUrl,
  //         staging: isStaging);

  //     paytmResponse.then((value) {
  //       print(value);
  //     });
  //   } catch (e) {
  //     print(e);
  //   }
  // }

  ///---------------------------Paystack Payment Integration
  // final plugin = PaystackPlugin();

  // init paystack
  // Future initPayStack() async {
  //   if (payStackpublicKey.isNotEmpty) {
  //     await plugin.initialize(publicKey: payStackpublicKey);
  //   }
  // }

  // show message
  void _showMessage(String message, context, Color? bgColor) {
    final snackBar = SnackBar(
      backgroundColor: bgColor,
      content: Text(message, style: TextStyle(color: AppColors.appWhiteColor)),
    );
    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }

  // refer
  String _getReference() {
    var platform = (Platform.isIOS) ? 'iOS' : 'Android';
    final thisDate = DateTime.now().millisecondsSinceEpoch;
    return 'ChargedFrom${platform}_$thisDate';
  }

  // checkout
  // payStackchargeCard(context) async {
  //   var charge = Charge()
  //     ..amount = double.parse(totalPayable).toInt() *
  //         100 //the money should be in kobo hence the need to multiply the value by 100
  //     // ..currency = sendCurrency
  //     ..reference = _getReference()
  //     ..putCustomField('custom_id',
  //         '846gey6w') //to pass extra parameters to be retrieved on the response from Paystack
  //     ..email = '<EMAIL>';
  //   CheckoutResponse response = await plugin.checkout(
  //     context,
  //     method: CheckoutMethod.card,
  //     charge: charge,
  //   );
  //   if (response.status == true) {
  //     onPaymentDone(
  //         Get.find<MoneyCalculationProceedController>().invoiceNumber.value,
  //         gatewayId);
  //     Get.offAll(() => ThankYouPage());
  //     _showMessage('Payment was successful!', context, AppColors.appGreenColor);
  //   } else {
  //     _showMessage('Payment Failed!', context, AppColors.appRedColor);
  //   }
  // }

  ///-----------------------------Paypal Payment Integration
  //Paypal checkout
  Future makePaypalPaymentRequest() async {
    var transactions = [
      {
        "amount": {
          "total": '$totalPayable',
          "currency": "$sendCurrency",
          "details": {
            "subtotal": '$totalPayable',
            "shipping": '0',
            "shipping_discount": 0
          }
        },
        "description": "The payment transaction description."
      }
    ];

    Get.to(
      () => PaypalCheckoutView(
        clientId: paypalClientId,
        secretKey: paypalSecretKey,
        transactions: transactions,
        note: "PAYMENT_NOTE",
        onSuccess: (Map params) async {
          onPaymentDone(
            Get.find<MoneyCalculationProceedController>().invoiceNumber.value,
            gatewayId,
          );
          Get.offAll(() => ThankYouPage());
        },
        onError: (error) {
          print("onError: $error");
          Get.back();
        },
        onCancel: () {
          print('cancelled:');
        },
      ),
    );
  }

  ///-----------------------------Flutter wave Payment Integration
  String flutterwaveTxRef = DateTime.now().toIso8601String();
  Flutterwave? flutterwave;
  Future handleFlutterwavePaymentInitialization(context) async {
    final Customer customer = Customer(
      name: "Flutterwave Developer",
      phoneNumber: "1234566677777",
      email: "<EMAIL>",
    );
    flutterwave = await Flutterwave(
      context: context,
      publicKey: "$flutterwavePublicKey",
      currency: "$sendCurrency",
      redirectUrl: "add-your-redirect-url-here",
      txRef: "$flutterwaveTxRef",
      amount: "$totalPayable",
      customer: customer,
      paymentOptions: "ussd, card, barter, payattitude",
      customization: Customization(title: "My Payment"),
      isTestMode: true,
    );
    final ChargeResponse response = await flutterwave!.charge();

    if (response.toJson()['success'] == true) {
      onPaymentDone(
        Get.find<MoneyCalculationProceedController>().invoiceNumber.value,
        gatewayId,
      );
      Get.to(() => ThankYouPage());
      Get.dialog(AppPaymentSuccess());
    } else {
      Get.dialog(AppPaymentFail());
      if (kDebugMode) {
        print("Something Went Wrong");
      }
    }
  }
}

// create a model class for dynamic form list of manual payment gateway
class ManualPaymentDynamicFormModel {
  String gatewayName;
  String fieldName;
  String fieldLevel;
  String type;
  String validation;

  ManualPaymentDynamicFormModel({
    required this.gatewayName,
    required this.fieldName,
    required this.fieldLevel,
    required this.type,
    required this.validation,
  });
}
