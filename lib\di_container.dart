import 'package:get/get.dart';
import 'package:Nestblock/data/repository/app_controller_repo.dart';
import 'controller/app_controller.dart';
import 'di_controller_index.dart';

final sl = GetIt.instance;

Future<void> init() async {
  /// Core
  sl.registerLazySingleton(() => DioClient(AppConstants.baseUri, sl(),
      loggingInterceptor: sl(), sharedPreferences: sl()));

  ///Repository
  sl.registerLazySingleton(
      () => AuthRepo(dioClient: sl(), sharedPreferences: sl()));
  sl.registerLazySingleton(
      () => UserRepo(dioClient: sl(), sharedPreferences: sl()));
  sl.registerLazySingleton(
      () => TransactionRepo(dioClient: sl(), sharedPreferences: sl()));
  sl.registerLazySingleton(
      () => PaymentRepo(dioClient: sl(), sharedPreferences: sl()));
  sl.registerLazySingleton(
      () => TwoFaSecurityRepo(dioClient: sl(), sharedPreferences: sl()));
  sl.registerLazySingleton(
      () => TransferLogRepo(dioClient: sl(), sharedPreferences: sl()));
  sl.registerLazySingleton(
      () => CountryListRepo(dioClient: sl(), sharedPreferences: sl()));
  sl.registerLazySingleton(() =>
      SendToReceiverCountryRepo(dioClient: sl(), sharedPreferences: sl()));
  sl.registerLazySingleton(
      () => CountryServiceRepo(dioClient: sl(), sharedPreferences: sl()));
  sl.registerLazySingleton(
      () => MoneyCalculationRepo(dioClient: sl(), sharedPreferences: sl()));
  sl.registerLazySingleton(() =>
      MoneyCalculationProceedRepo(dioClient: sl(), sharedPreferences: sl()));
  sl.registerLazySingleton(
      () => CreateTicketRepo(dioClient: sl(), sharedPreferences: sl()));
  sl.registerLazySingleton(
      () => TicketListRepo(dioClient: sl(), sharedPreferences: sl()));
  sl.registerLazySingleton(
      () => ReplyTicketRepo(dioClient: sl(), sharedPreferences: sl()));
  sl.registerLazySingleton(
      () => ViewTicketRepo(dioClient: sl(), sharedPreferences: sl()));
  sl.registerLazySingleton(
      () => VerificationRepo(dioClient: sl(), sharedPreferences: sl()));
  sl.registerLazySingleton(
      () => NotificationRepo(dioClient: sl(), sharedPreferences: sl()));
  sl.registerLazySingleton(
      () => PaymentGatewayRepo(dioClient: sl(), sharedPreferences: sl()));
  sl.registerLazySingleton(
      () => AppControllerRepo(dioClient: sl(), sharedPreferences: sl()));

  /// Controller
  Get.lazyPut(() => BottomNavController(), fenix: true);

  Get.lazyPut(() => AuthController(authRepo: sl(), dioClient: sl()),
      fenix: true);
  Get.lazyPut(() => UserController(userRepo: sl()), fenix: true);
  Get.lazyPut(() => TransactionController(transactionRepo: sl()), fenix: true);
  Get.lazyPut(() => PaymentHistoryController(paymentRepo: sl()), fenix: true);
  Get.lazyPut(() => TwoFaSecurityController(twoFaSecurityRepo: sl()),
      fenix: true);
  Get.lazyPut(() => TransferLogController(transferLogRepo: sl()), fenix: true);
  Get.lazyPut(() => CountryListController(countryListRepo: sl()), fenix: true);
  Get.lazyPut(
      () => SendToReceiverCountryController(sendToReceiverCountryRepo: sl()),
      fenix: true);
  Get.lazyPut(() => CountryServiceController(countryServiceRepo: sl()),
      fenix: true);
  Get.lazyPut(() => MoneyCalculationController(moneyCalculationRepo: sl()),
      fenix: true);
  Get.lazyPut(
      () =>
          MoneyCalculationProceedController(moneyCalculationProceedRepo: sl()),
      fenix: true);
  Get.lazyPut(() => CreateTicketController(createTicketRepo: sl()),
      fenix: true);
  Get.lazyPut(() => TicketListController(ticketListRepo: sl()), fenix: true);
  Get.lazyPut(() => ReplyTicketController(replyTicketRepo: sl()), fenix: true);
  Get.lazyPut(() => ViewTicketController(viewTicketRepo: sl()), fenix: true);
  Get.lazyPut(() => VerificationController(verificationRepo: sl()),
      fenix: true);
  Get.lazyPut(() => AppController(appControllerRepo: sl()), fenix: true);
  // Get.put(() => StripePaymentController());
  Get.put(() => PaymentGatewayController(paymentGatewayRepo: sl()));
  Get.put(
    () => PushNotificationController(notificationRepo: sl()),
  );

  /// External pocket lock
  final sharedPreferences = await SharedPreferences.getInstance();
  final box = await GetStorage.init();
  sl.registerLazySingleton(() => sharedPreferences);
  sl.registerLazySingleton(() => box);
  sl.registerLazySingleton(() => Dio());
  sl.registerLazySingleton(() => LoggingInterceptor());
}
