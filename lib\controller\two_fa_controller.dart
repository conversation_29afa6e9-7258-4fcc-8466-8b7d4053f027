import 'package:Nestblock/routes/page_index.dart';

import '../data/model/base_model/api_response.dart';
import '../data/model/response/two_fa_model.dart';
import '../data/repository/two_fa_repo.dart';
import '../utils/app_colors.dart';
import '../utils/helpers.dart';
import '../view/screens/auth/signin_screen.dart';
import '../view/screens/profile/profile_setting_screen.dart';
import '../view/screens/profile/two_fa_screen.dart';
import '../view/widgets/app_bottom_sheet.dart';
import 'auth_controller.dart';

class TwoFaSecurityController extends GetxController {
  final TwoFaSecurityRepo twoFaSecurityRepo;

  TwoFaSecurityController({required this.twoFaSecurityRepo});

  final googleAuthEnableCode = TextEditingController();
  final googleAuthDisableCode = TextEditingController();

  String? _status;
  String? get status => _status;
  TwoFaSecurityData? _message;
  TwoFaSecurityData? get message => _message;

  bool _isLoading = false;
  bool get isLoading => _isLoading;

  TwoFactorSecurityModel twoFactorSecurityModel = TwoFactorSecurityModel();

  ///Two Factor Security Data
  Future<dynamic> getTwoFaSecurityData() async {
    _isLoading = true;
    update();
    ApiResponse apiResponse = await twoFaSecurityRepo.getTwoFaSecurityData();

    if (apiResponse.response != null &&
        apiResponse.response!.statusCode == 200) {
      _isLoading = false;
      update();
      if (apiResponse.response!.data != null) {
        print("api response is : ${apiResponse.response!.data}");
        var res = apiResponse.response!.data;
        if (res['message'] == "Email Verification Required") {
          _isLoading = false;

          Helpers.showSnackBar(msg: res['message']);
          await appBottomSheet();
        } else if (res['message'] == "Mobile Verification Required") {
          _isLoading = false;
          Helpers.showSnackBar(msg: res['message']);
          await appBottomSheet(isMailVerification: false);
        } else if (res['message'] == "Two FA Verification Required") {
          _isLoading = false;
          Helpers.showSnackBar(msg: res['message']);
          Get.offAll(TwoFaScreen(isTwofaVerification: true));
        } else if (res['message'] == "Your account has been suspend") {
          _isLoading = false;
          Helpers.showSnackBar(msg: res['message']);
          Get.find<AuthController>().removeUserToken();
          Get.offAllNamed(SignInScreen.routeName);
        } else if (res['message'] == "Identity Verification Required") {
          _isLoading = false;
          Helpers.showSnackBar(msg: res['message']);
          Get.offAll(ProfileSettingScreen(isIdentityVerification: true));
        } else if (res['message'] == "Address Verification Required") {
          _isLoading = false;
          Helpers.showSnackBar(msg: res['message']);
          Get.offAll(ProfileSettingScreen(isAddressVerification: true));
        } else {
          _message = null;
          update();
          twoFactorSecurityModel =
              TwoFactorSecurityModel.fromJson(apiResponse.response!.data!);
          _message = twoFactorSecurityModel.message;
        }

        update();
      }
    } else {
      _isLoading = false;
      update();
    }
  }

  ///Enable Two Factor Security
  Future<dynamic> enableTwoFactorSecurity(
    dynamic code,
    dynamic key,
  ) async {
    _isLoading = true;
    update();
    ApiResponse apiResponse =
        await twoFaSecurityRepo.enableTwoFactorSecurity(code, key);

    if (apiResponse.response != null &&
        apiResponse.response!.statusCode == 200) {
      _isLoading = false;
      update();
      if (apiResponse.response!.data != null) {
        dynamic status = apiResponse.response!.data['status'];
        dynamic msg = apiResponse.response!.data['message'];
        Get.snackbar(
          'Message',
          '${msg}',
          backgroundColor:
              status == "success" ? AppColors.appGreenColor : Colors.red,
          colorText: Colors.white,
          duration: Duration(seconds: 2),
          snackPosition: SnackPosition.BOTTOM,
          margin: EdgeInsets.all(10),
          borderRadius: 8,
          shouldIconPulse: true,
          icon: Icon(status == "success" ? Icons.check : Icons.cancel,
              color: Colors.white),
          barBlur: 10,
        );
        update();
      }
    } else {
      _isLoading = false;
      update();
    }
  }

  ///Disable Two Factor Security
  Future<dynamic> disableTwoFactorSecurity(dynamic code) async {
    _isLoading = true;
    update();
    ApiResponse apiResponse =
        await twoFaSecurityRepo.disableTwoFactorSecurity(code);

    if (apiResponse.response != null &&
        apiResponse.response!.statusCode == 200) {
      _isLoading = false;
      update();
      if (apiResponse.response!.data != null) {
        dynamic status = apiResponse.response!.data['status'];
        dynamic msg = apiResponse.response!.data['message'];
        Get.snackbar(
          'Message',
          '${msg}',
          backgroundColor:
              status == "success" ? AppColors.appGreenColor : Colors.red,
          colorText: Colors.white,
          duration: Duration(seconds: 2),
          snackPosition: SnackPosition.BOTTOM,
          margin: EdgeInsets.all(10),
          borderRadius: 8,
          shouldIconPulse: true,
          icon: Icon(status == "success" ? Icons.check : Icons.cancel,
              color: Colors.white),
          barBlur: 10,
        );
        update();
      }
    } else {
      _isLoading = false;
      update();
    }
  }

  @override
  void onInit() {
    // TODO: implement onInit
    getTwoFaSecurityData();
    super.onInit();
  }
}
