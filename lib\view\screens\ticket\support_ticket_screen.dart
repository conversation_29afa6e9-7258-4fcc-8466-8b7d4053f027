import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:Nestblock/controller/ticket_list_controller.dart';
import 'package:Nestblock/utils/helpers.dart';
import 'package:Nestblock/view/screens/ticket/ticket_reply_screen.dart';

import '../../../controller/app_controller.dart';
import '../../../utils/app_colors.dart';
import '../../../utils/local_storage.dart';
import '../../widgets/flexible_space_widget.dart';
import 'create_ticket_screen.dart';

class SupportTicketScreen extends StatelessWidget {
  static const String routeName = "/supportTicketScreen";
  const SupportTicketScreen({super.key});

  @override
  Widget build(BuildContext context) {
    var storedLanguage = LocalStorage.get(LocalStorage.languageData) ?? {};
    return GetBuilder<TicketListController>(builder: (ticketListController) {
      return Scaffold(
        backgroundColor: AppColors.appPrimaryColor,
        appBar: PreferredSize(
          preferredSize: Size.fromHeight(60.h),
          child: AppBar(
            centerTitle: true,
            backgroundColor: AppColors.appPrimaryColor,
            leading: GestureDetector(
              onTap: () {
                Navigator.pop(context);
              },
              child: Icon(
                Icons.arrow_back,
                color: AppColors.appWhiteColor,
              ),
            ),
            automaticallyImplyLeading: false,
            titleSpacing: 5,
            elevation: 1,
            flexibleSpace: FlexibleSpaceWidget(),
            title: Text(
              storedLanguage['Support Ticket'] ?? "Support Ticket",
              style: TextStyle(
                  fontSize: 24.sp,
                  color: AppColors.appWhiteColor,
                  fontWeight: FontWeight.w500),
              textAlign: TextAlign.center,
            ),
          ),
        ),
        floatingActionButton: FloatingActionButton.extended(
          backgroundColor: AppColors.appPrimaryColor,
          onPressed: () {
            Get.toNamed(CreateTicketScreen.routeName);
          },
          label: Row(
            children: [
              Padding(
                padding: const EdgeInsets.only(right: 4.0),
                child: Icon(
                  Icons.add,
                  color: AppColors.appWhiteColor,
                  size: 20.h,
                ),
              ),
              Text(
                storedLanguage['Create Ticket'] ?? "Create Ticket",
                style: TextStyle(
                  color: AppColors.appWhiteColor,
                  fontSize: 18.sp,
                ),
              )
            ],
          ),
        ),
        body: RefreshIndicator(
          onRefresh: () async {
            ticketListController.resetData();
            await ticketListController.getTicketListData(page: 1);
          },
          child: Column(
            children: [
              Expanded(
                child: Container(
                  width: double.maxFinite,
                  height: MediaQuery.sizeOf(context).height,
                  decoration: BoxDecoration(
                      color: Get.find<AppController>().getDarkBgColor(),
                      borderRadius:
                          BorderRadius.vertical(top: Radius.circular(30.r))),
                  child: ticketListController.isLoading == true
                      ? Column(
                          children: [
                            SizedBox(height: 20.h),
                            Helpers.appLoader(),
                          ],
                        )
                      : ticketListController.allTicketList.isEmpty
                          ? Center(
                              child: Image.asset(
                              "assets/images/not_found.png",
                              fit: BoxFit.cover,
                              height: 150.h,
                            ))
                          : ListView.builder(
                              controller: ticketListController.scrollController,
                              padding: EdgeInsets.only(top: 20.h),
                              itemCount:
                                  ticketListController.allTicketList.length,
                              shrinkWrap: true,
                              itemBuilder: (context, index) {
                                var data =
                                    ticketListController.allTicketList[index];
                                return Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 32),
                                  child: GestureDetector(
                                    onTap: () {
                                      Get.to(
                                        TicketViewReplyScreen(
                                            ticketId: data.ticket),
                                      );
                                    },
                                    child: Stack(
                                      clipBehavior: Clip.hardEdge,
                                      children: [
                                        LocalStorage.get(LocalStorage.isDark) ==
                                                null
                                            ? Image.asset(
                                                "assets/images/bg_ticket.png",
                                                width: 326.w,
                                                height: 91.h,
                                              )
                                            : LocalStorage.get(
                                                    LocalStorage.isDark)
                                                ? Image.asset(
                                                    "assets/images/bg_ticket_dark.png",
                                                    width: 326.w,
                                                    height: 91.h,
                                                  )
                                                : Image.asset(
                                                    "assets/images/bg_ticket.png",
                                                    width: 326.w,
                                                    height: 91.h,
                                                  ),
                                        Positioned(
                                          top: 16.h,
                                          left: 20.w,
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Container(
                                                width:
                                                    MediaQuery.sizeOf(context)
                                                            .width *
                                                        .7,
                                                child: Text(
                                                  "${data.subject}",
                                                  maxLines: 1,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                  style: TextStyle(
                                                      fontSize: 18.sp,
                                                      color: LocalStorage.get(
                                                                      LocalStorage
                                                                          .isDark) ==
                                                                  null ||
                                                              LocalStorage.get(
                                                                      LocalStorage
                                                                          .isDark) ==
                                                                  false
                                                          ? AppColors
                                                              .appTicketTitleColor
                                                          : Colors.white,
                                                      fontWeight:
                                                          FontWeight.w500),
                                                ),
                                              ),
                                              SizedBox(
                                                height: 2.h,
                                              ),
                                              Container(
                                                width: 279.w,
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    Row(
                                                      children: [
                                                        Text(
                                                          "${storedLanguage['Status'] ?? "Status : "}",
                                                          style: TextStyle(
                                                              fontSize: 18.sp,
                                                              fontFamily:
                                                                  'Dubai',
                                                              color: AppColors
                                                                  .appBlackColor,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .normal),
                                                        ),
                                                        Text(
                                                          " ${data.status}",
                                                          style: TextStyle(
                                                              fontSize: 16.sp,
                                                              fontFamily:
                                                                  'Dubai',
                                                              color: data.status ==
                                                                      "Closed"
                                                                  ? AppColors
                                                                      .appSendMoneyRed
                                                                  : AppColors
                                                                      .appTicketStatusColor,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w400),
                                                        ),
                                                      ],
                                                    ),
                                                    Text(
                                                      "${data.lastReply}",
                                                      style: TextStyle(
                                                          fontSize: 16.sp,
                                                          color: AppColors
                                                              .appTicketTimeColor,
                                                          fontWeight: FontWeight
                                                              .normal),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              }),
                ),
              ),
              if (ticketListController.isLoadMore == true)
                Container(
                    color: AppColors.appWhiteColor,
                    padding: EdgeInsets.only(top: 10.h, bottom: 40.h),
                    child: Helpers.appLoader()),
            ],
          ),
        ),
      );
    });
  }
}
