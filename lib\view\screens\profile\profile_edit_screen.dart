import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:Nestblock/controller/app_controller.dart';
import 'package:Nestblock/controller/user_controller.dart';

import '../../../utils/app_colors.dart';
import '../../../utils/helpers.dart';
import '../../../utils/local_storage.dart';
import '../../widgets/profile_header_widget.dart';

class ProfileEditScreen extends StatefulWidget {
  static const String routeName = "/profileEditSettingScreen";
  const ProfileEditScreen({super.key});

  @override
  State<ProfileEditScreen> createState() => _ProfileEditScreenState();
}

class _ProfileEditScreenState extends State<ProfileEditScreen> {
  XFile? pickedImage;

  Future<void> pickImage(ImageSource source) async {
    final checkPermission = await Permission.camera.request();
    if (checkPermission.isGranted) {
      final picker = ImagePicker();
      final pickedImageFile = await picker.pickImage(source: source);

      setState(() {
        pickedImage = pickedImageFile;
        if (pickedImage != null) {
          user
              .updateUserInformation(
                  user.firstName.text.toString(),
                  user.lastName.text.toString(),
                  user.userName.text.toString(),
                  user.address.text.toString(),
                  path: pickedImage!.path,
                  context)
              .then((value) {
            user.getUserData();
          });
        }
        Navigator.pop(context);
      });
    } else {
      Helpers.showSnackBar(
          msg:
              "Please grant camera permission in app settings to use this feature.");
    }
  }

  final user = Get.find<UserController>();

  // @override
  // void initState() {
  //   user.firstName.text = "${user.data!.user!.firstname}";
  //   user.lastName.text = "${user.data!.user!.lastname}";
  //   user.userName.text = "${user.data!.user!.username}";
  //   user.address.text =
  //       user.data!.user!.address == null ? "" : "${user.data!.user!.address}";
  //   super.initState();
  // }

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    var storedLanguage = LocalStorage.get(LocalStorage.languageData) ?? {};
    return GetBuilder<UserController>(builder: (userController) {
      return Scaffold(
        backgroundColor: Get.find<AppController>().getDarkBgColor(),
        appBar: ProfileHeaderWidget(
            isProfileEdit: true,
            isProfileEmpty: user.data == null
                ? false
                : (user.data!.user!.image != null)
                    ? false
                    : true,
            onPressed: () {
              showbottomsheet(context, storedLanguage);
            },
            profilePhoto: ClipOval(
              child: pickedImage != null
                  ? Image.file(
                      File(pickedImage!.path),
                      height: 80.0.h,
                      width: 80.0.h,
                      fit: BoxFit.cover,
                    )
                  : user.data != null && (user.data!.user!.image != null)
                      ? CachedNetworkImage(
                          imageUrl: "${userController.data!.user!.photo}",
                          height: 80.0.h,
                          width: 80.0.h,
                          fit: BoxFit.cover,
                          placeholder: (context, url) =>
                              const CircularProgressIndicator(), // Placeholder while loading
                          errorWidget: (context, url, error) => const Icon(
                              Icons.error), // Error widget if loading fails
                        )
                      : Icon(Icons.account_circle, size: 80.h),
            ),
            profileName: userController.data == null
                ? ""
                : "${userController.data!.user!.firstname} ${userController.data!.user!.lastname}",
            joinedDate: userController.data == null
                ? ""
                : "${storedLanguage['Joined At'] ?? "Joined At"} ${DateFormat('dd MMM yyyy').format(DateTime.parse(userController.data!.user!.createdAt).toLocal())}"),
        body: RefreshIndicator(
          onRefresh: () async {
            await userController.getUserData();
          },
          child: ListView(
            children: [
              SizedBox(
                height: 20.h,
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 32.w),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        storedLanguage['Profile Information'] ??
                            "Profile Information",
                        style: TextStyle(
                            fontSize: 20.sp,
                            color: AppColors.appBlackColor,
                            fontWeight: FontWeight.w400),
                      ),
                      SizedBox(
                        height: 24.h,
                      ),
                      Text(
                        storedLanguage['First Name'] ?? "First Name",
                        style: TextStyle(
                            fontSize: 16.sp,
                            color: AppColors.appBlack50,
                            fontWeight: FontWeight.w400),
                      ),
                      SizedBox(
                        height: 4.h,
                      ),
                      TextFormField(
                        validator: (value) {
                          if (value!.isEmpty) {
                            return 'First name is required';
                          }
                          return null; // Return null if the input is valid.
                        },
                        controller: user.firstName,
                        decoration: InputDecoration(
                          contentPadding: const EdgeInsets.symmetric(
                              vertical: 0, horizontal: 16),
                          filled: true, // Fill the background with color

                          hintStyle: TextStyle(
                            color: AppColors.appBlack30,
                          ),
                          fillColor: Get.find<AppController>()
                              .getDarkBgTextFieldColorDefault(), // Background color
                          enabledBorder: OutlineInputBorder(
                            borderSide: BorderSide(
                              color: Get.find<AppController>()
                                  .getDarkBgTextFieldEnableBorderColorDefault(),
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderSide:
                                BorderSide(color: AppColors.appPrimaryColor),
                          ),
                        ),
                        style: TextStyle(color: AppColors.appBlackColor),
                      ),
                      SizedBox(
                        height: 12.h,
                      ),
                      Text(
                        storedLanguage['Last Name'] ?? "Last Name",
                        style: TextStyle(
                            fontSize: 16.sp,
                            color: AppColors.appBlack50,
                            fontWeight: FontWeight.w400),
                      ),
                      SizedBox(
                        height: 4.h,
                      ),
                      TextFormField(
                        validator: (value) {
                          if (value!.isEmpty) {
                            return 'Last name is required';
                          }
                          return null; // Return null if the input is valid.
                        },
                        controller: user.lastName,
                        decoration: InputDecoration(
                          contentPadding: const EdgeInsets.symmetric(
                              vertical: 0, horizontal: 16),
                          filled: true, // Fill the background with color
                          hintStyle: TextStyle(
                            color: AppColors.appBlack30,
                          ),
                          fillColor: Get.find<AppController>()
                              .getDarkBgTextFieldColorDefault(), // Background color
                          enabledBorder: OutlineInputBorder(
                            borderSide: BorderSide(
                              color: Get.find<AppController>()
                                  .getDarkBgTextFieldEnableBorderColorDefault(),
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderSide:
                                BorderSide(color: AppColors.appPrimaryColor),
                          ),
                        ),
                        style: TextStyle(color: AppColors.appBlackColor),
                      ),
                      SizedBox(
                        height: 12.h,
                      ),
                      Text(
                        storedLanguage['Username'] ?? "Username",
                        style: TextStyle(
                            fontSize: 16.sp,
                            color: AppColors.appBlack50,
                            fontWeight: FontWeight.w400),
                      ),
                      SizedBox(
                        height: 4.h,
                      ),
                      TextFormField(
                        validator: (value) {
                          if (value!.isEmpty) {
                            return 'Username name is required';
                          }
                          return null; // Return null if the input is valid.
                        },
                        controller: user.userName,
                        decoration: InputDecoration(
                          contentPadding: const EdgeInsets.symmetric(
                              vertical: 0, horizontal: 16),
                          filled: true, // Fill the background with color
                          hintStyle: TextStyle(
                            color: AppColors.appBlack30,
                          ),
                          fillColor: Get.find<AppController>()
                              .getDarkBgTextFieldColorDefault(), // Background color
                          enabledBorder: OutlineInputBorder(
                            borderSide: BorderSide(
                              color: Get.find<AppController>()
                                  .getDarkBgTextFieldEnableBorderColorDefault(),
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderSide:
                                BorderSide(color: AppColors.appPrimaryColor),
                          ),
                        ),
                        style: TextStyle(color: AppColors.appBlackColor),
                      ),
                      SizedBox(
                        height: 12.h,
                      ),
                      // Text(
                      //   "Address",
                      //   style: TextStyle(
                      //       fontSize: 16.sp,
                      //
                      //       color: AppColors.appBlack50,
                      //       fontWeight: FontWeight.w400),
                      // ),
                      // SizedBox(
                      //   height: 4.h,
                      // ),
                      // TextFormField(
                      //   controller: user.address,
                      //   decoration: InputDecoration(
                      //     contentPadding: const EdgeInsets.symmetric(
                      //         vertical: 0, horizontal: 16),
                      //     filled: true, // Fill the background with color
                      //     fillColor:
                      //         AppColors.appInputFieldColor, // Background color
                      //     enabledBorder: OutlineInputBorder(
                      //       borderSide: BorderSide(color: AppColors.appBlack10),
                      //     ),
                      //     focusedBorder: OutlineInputBorder(
                      //       borderSide:
                      //           BorderSide(color: AppColors.appPrimaryColor),
                      //     ),
                      //   ),
                      // ),
                      // SizedBox(
                      //   height: 12.h,
                      // ),
                      // Text(
                      //   "Preferred language",
                      //   style: TextStyle(
                      //       fontSize: 16.sp,
                      //
                      //       color: AppColors.appBlack50,
                      //       fontWeight: FontWeight.w400),
                      // ),
                      // SizedBox(
                      //   height: 4.h,
                      // ),
                      // TextFormField(
                      //   decoration: InputDecoration(
                      //     contentPadding: const EdgeInsets.symmetric(
                      //         vertical: 0, horizontal: 16),
                      //     filled: true, // Fill the background with color
                      //     fillColor:
                      //         AppColors.appInputFieldColor, // Background color
                      //     enabledBorder: OutlineInputBorder(
                      //       borderSide: BorderSide(color: AppColors.appBlack10),
                      //     ),
                      //     focusedBorder: OutlineInputBorder(
                      //       borderSide:
                      //           BorderSide(color: AppColors.appPrimaryColor),
                      //     ),
                      //   ),
                      // ),
                      SizedBox(
                        height: 12.h,
                      ),
                      Text(
                        storedLanguage['Address'] ?? "Address",
                        style: TextStyle(
                            fontSize: 16.sp,
                            color: AppColors.appBlack50,
                            fontWeight: FontWeight.w400),
                      ),
                      SizedBox(
                        height: 4.h,
                      ),
                      TextFormField(
                        validator: (value) {
                          if (value!.isEmpty) {
                            return 'Address is required';
                          }
                          return null; // Return null if the input is valid.
                        },
                        controller: user.address,
                        maxLines: null,
                        minLines: 4,
                        decoration: InputDecoration(
                          contentPadding: const EdgeInsets.symmetric(
                              vertical: 0, horizontal: 16),
                          filled: true, // Fill the background with color
                          hintStyle: TextStyle(
                            color: AppColors.appBlack30,
                          ),
                          fillColor: Get.find<AppController>()
                              .getDarkBgTextFieldColorDefault(), // Background color
                          enabledBorder: OutlineInputBorder(
                            borderSide: BorderSide(
                              color: Get.find<AppController>()
                                  .getDarkBgTextFieldEnableBorderColorDefault(),
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderSide:
                                BorderSide(color: AppColors.appPrimaryColor),
                          ),
                        ),
                        style: TextStyle(color: AppColors.appBlackColor),
                      ),
                      SizedBox(
                        height: 24.h,
                      ),
                      GestureDetector(
                        onTap: () {
                          if (_formKey.currentState!.validate()) {
                            if (kDebugMode) {
                              print(userController.data!.user!.image);
                            }
                            pickedImage != null
                                ? user
                                    .updateUserInformation(
                                        user.firstName.text.toString(),
                                        user.lastName.text.toString(),
                                        user.userName.text.toString(),
                                        user.address.text.toString(),
                                        path: pickedImage!.path,
                                        context)
                                    .then((value) {
                                    user.getUserData();
                                  })
                                : user
                                    .updateUserInformation(
                                    user.firstName.text.toString(),
                                    user.lastName.text.toString(),
                                    user.userName.text.toString(),
                                    user.address.text.toString(),
                                    context,
                                  )
                                    .then((value) {
                                    user.getUserData();
                                  });
                          }
                        },
                        child: Container(
                          height: 45.h,
                          width: 326.w,
                          decoration: BoxDecoration(
                              color: AppColors.appPrimaryColor,
                              borderRadius: BorderRadius.circular(4)),
                          child: Center(
                            child: user.isLoadingImage == false
                                ? Text(
                                    storedLanguage['Update Profile'] ??
                                        "Update Profile",
                                    style: TextStyle(
                                        fontSize: 18.sp,
                                        color: AppColors.appWhiteColor,
                                        fontWeight: FontWeight.w500),
                                  )
                                : CircularProgressIndicator(
                                    color: AppColors.appWhiteColor,
                                  ),
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 24.h,
                      ),
                    ],
                  ),
                ),
              )
            ],
          ),
        ),
      );
    });
  }

  Future<dynamic> showbottomsheet(BuildContext context, storage) {
    return showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SizedBox(
          height: MediaQuery.of(context).size.height * 0.2,
          width: double.infinity,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              SizedBox(height: 10.h),
              GestureDetector(
                onTap: () {
                  pickImage(ImageSource.camera);
                },
                child: Container(
                  height: 80.h,
                  width: 150.w,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(5),
                      color: AppColors.appWhiteColor,
                      border: Border.all(color: AppColors.appBlack10)),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.camera_alt,
                        size: 35.h,
                        color: AppColors.appBlack50,
                      ),
                      Text(
                        storage['Pick from Camera'] ?? 'Pick from Camera',
                        style: TextStyle(fontSize: 14.sp),
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(
                width: 10.w,
              ),
              GestureDetector(
                onTap: () {
                  pickImage(ImageSource.gallery);
                },
                child: Container(
                  height: 80.h,
                  width: 150.w,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(5),
                      color: AppColors.appWhiteColor,
                      border: Border.all(color: AppColors.appBlack10)),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.camera,
                        size: 35.h,
                        color: AppColors.appBlack70,
                      ),
                      Text(
                        storage['Pick from Gallery'] ?? 'Pick from Gallery',
                        style: TextStyle(fontSize: 14.sp),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
