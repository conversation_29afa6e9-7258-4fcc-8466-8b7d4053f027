import 'package:Nestblock/utils/helpers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../controller/auth_controller.dart';
import '../../../utils/app_colors.dart';

class ForgetPassword extends StatelessWidget {
  const ForgetPassword({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AuthController>(builder: (_) {
      return ListView(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Forgot Password",
                style: TextStyle(
                    fontSize: 20.sp,
                    color: AppColors.appBlackColor,
                    fontWeight: FontWeight.w500),
              ),
              SizedBox(
                height: 8.h,
              ),
              Text(
                "Enter your email for the verification process, we will send 4 digits code to your email.",
                style: TextStyle(
                    fontSize: 16.sp,
                    color: AppColors.appBlack50,
                    fontWeight: FontWeight.normal),
              ),
              SizedBox(
                height: 24.h,
              ),
              Text(
                "Email",
                style: TextStyle(
                    fontSize: 18.sp,
                    color: AppColors.appBlack40,
                    fontWeight: FontWeight.normal),
              ),
              SizedBox(
                height: 4.h,
              ),
              TextFormField(
                controller:
                    Get.find<AuthController>().forgotPassEmailTextEditingCtrlr,
                decoration: InputDecoration(
                  hintText: "Enter your email",
                  hintStyle: TextStyle(
                      fontSize: 16.sp,
                      color: AppColors.appBlack30,
                      fontWeight: FontWeight.normal),
                  contentPadding:
                      const EdgeInsets.symmetric(vertical: 0, horizontal: 16),
                  filled: true, // Fill the background with color
                  fillColor: AppColors.appInputFieldColor, // Background color
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: AppColors.appWhiteColor),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: AppColors.appPrimaryColor),
                  ),
                ),
              ),
              SizedBox(
                height: 32.h,
              ),
              Obx(
                () => GestureDetector(
                  onTap:
                      Get.find<AuthController>().isLoadingForgetPassword.value
                          ? null
                          : () async {
                              if (Get.find<AuthController>()
                                  .forgotPassEmailTextEditingCtrlr
                                  .text
                                  .isEmpty) {
                                Helpers().showValidationErrorDialog(
                                    errorText: "Please enter your email");
                              } else {
                                await Get.find<AuthController>()
                                    .forgetPasswordRequest(
                                        context,
                                        Get.find<AuthController>()
                                            .forgotPassEmailTextEditingCtrlr
                                            .text);
                              }
                            },
                  child: Container(
                    height: 46.h,
                    width: 326.w,
                    decoration: BoxDecoration(
                        color: AppColors.appPrimaryColor,
                        borderRadius: BorderRadius.circular(4)),
                    child: Center(
                      child: Get.find<AuthController>()
                              .isLoadingForgetPassword
                              .value
                          ? Helpers.appLoader(color: AppColors.appWhiteColor)
                          : Text(
                              "Continue",
                              style: TextStyle(
                                  fontSize: 20.sp,
                                  color: AppColors.appWhiteColor,
                                  fontWeight: FontWeight.w500),
                            ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      );
    });
  }
}
