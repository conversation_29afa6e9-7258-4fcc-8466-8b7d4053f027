                        -HC:\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-DANDROID_PLATFORM=android-24
-DANDROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\26.3.11579264
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\26.3.11579264
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\26.3.11579264\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\Flutter Projects\Nestblock\Nestblock Mobile\build\app\intermediates\cxx\RelWithDebInfo\676v6a4b\obj\armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\Flutter Projects\Nestblock\Nestblock Mobile\build\app\intermediates\cxx\RelWithDebInfo\676v6a4b\obj\armeabi-v7a
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-BD:\Flutter Projects\Nestblock\Nestblock Mobile\android\app\.cxx\RelWithDebInfo\676v6a4b\armeabi-v7a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2