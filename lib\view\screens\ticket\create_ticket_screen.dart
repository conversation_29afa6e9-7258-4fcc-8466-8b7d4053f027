import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:Nestblock/controller/create_ticket_controller.dart';
import 'package:Nestblock/controller/ticket_list_controller.dart';
import 'package:Nestblock/utils/helpers.dart';

import '../../../controller/app_controller.dart';
import '../../../utils/app_colors.dart';
import '../../../utils/local_storage.dart';
import '../../widgets/flexible_space_widget.dart';

class CreateTicketScreen extends StatefulWidget {
  static const String routeName = "/createTicketScreen";
  const CreateTicketScreen({super.key});

  @override
  State<CreateTicketScreen> createState() => _CreateTicketScreenState();
}

class _CreateTicketScreenState extends State<CreateTicketScreen> {
  final List<String> _fileNames = [];

  FilePickerResult? result;
  List<dynamic> selectedFilePaths = []; // Store all selected file paths

  Future<void> _pickFiles() async {
    // Request storage permission
    final storageStatus = await Permission.storage.request();

    if (storageStatus.isGranted) {
      try {
        result = await FilePicker.platform.pickFiles(
          allowMultiple: true,
        );

        if (result != null) {
          setState(() {
            if (kDebugMode) {
              print(result!.paths);
            }
            _fileNames.addAll(result!.paths.map((path) => path!));
            selectedFilePaths.addAll(result!.paths
                .whereType<String>()); // Add selected paths to the list
          });
        }
      } catch (e) {
        if (kDebugMode) {
          print("Error while picking files: $e");
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    var storedLanguage = LocalStorage.get(LocalStorage.languageData) ?? {};
    return GetBuilder<CreateTicketController>(
        builder: (createTicketController) {
      return Scaffold(
        backgroundColor: AppColors.appPrimaryColor,
        appBar: PreferredSize(
          preferredSize: Size.fromHeight(60.h),
          child: AppBar(
            centerTitle: true,
            backgroundColor: AppColors.appPrimaryColor,
            leading: GestureDetector(
              onTap: () {
                Navigator.pop(context);
              },
              child: Icon(
                Icons.arrow_back,
                color: AppColors.appWhiteColor,
              ),
            ),
            automaticallyImplyLeading: false,
            titleSpacing: 5,
            elevation: 1,
            flexibleSpace: FlexibleSpaceWidget(),
            title: Text(
              storedLanguage['Create Ticket'] ?? "Create Ticket",
              style: TextStyle(
                  fontSize: 24.sp,
                  color: AppColors.appWhiteColor,
                  fontWeight: FontWeight.w500),
              textAlign: TextAlign.center,
            ),
          ),
        ),
        body: Column(
          children: [
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                    color: Get.find<AppController>().getDarkBgColor(),
                    borderRadius:
                        BorderRadius.vertical(top: Radius.circular(30.r))),
                child: Padding(
                  padding: EdgeInsets.only(left: 32.w, right: 32.w, top: 20.h),
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          storedLanguage['Subject'] ?? "Subject",
                          style: TextStyle(
                              fontSize: 16.sp,
                              color: AppColors.appBlackColor,
                              fontWeight: FontWeight.normal),
                        ),
                        SizedBox(
                          height: 4.h,
                        ),
                        TextFormField(
                          controller: createTicketController.subjectCtrl,
                          decoration: InputDecoration(
                            hintText: storedLanguage['Enter Subject'] ??
                                "Enter Subject",
                            hintStyle: TextStyle(
                                color: AppColors.appBlack50,
                                fontSize: 16.sp,
                                fontWeight: FontWeight.normal),
                            contentPadding: const EdgeInsets.symmetric(
                                vertical: 0, horizontal: 16),
                            filled: true, // Fill the background with color

                            fillColor: Get.find<AppController>()
                                .getDarkBgTextFieldColorDefault(), // Background color
                            enabledBorder: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: Get.find<AppController>()
                                    .getDarkBgTextFieldEnableBorderColorDefault(),
                              ),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderSide:
                                  BorderSide(color: AppColors.appPrimaryColor),
                            ),
                          ),
                          style: TextStyle(color: AppColors.appBlackColor),
                        ),
                        SizedBox(
                          height: 16.h,
                        ),
                        Text(
                          storedLanguage['Message'] ?? "Message",
                          style: TextStyle(
                              fontSize: 16.sp,
                              color: AppColors.appBlackColor,
                              fontWeight: FontWeight.normal),
                        ),
                        SizedBox(
                          height: 4.h,
                        ),
                        TextFormField(
                          controller: createTicketController.messageCtrl,
                          maxLines: null,
                          minLines: 4,
                          decoration: InputDecoration(
                            hintText: storedLanguage['Enter Message'] ??
                                "Enter Message",
                            hintStyle: TextStyle(
                                color: AppColors.appBlack50,
                                fontSize: 16.sp,
                                fontWeight: FontWeight.normal),
                            contentPadding: const EdgeInsets.symmetric(
                                vertical: 4, horizontal: 16),
                            filled: true, // Fill the background with color

                            fillColor: Get.find<AppController>()
                                .getDarkBgTextFieldColorDefault(), // Background color
                            enabledBorder: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: Get.find<AppController>()
                                    .getDarkBgTextFieldEnableBorderColorDefault(),
                              ),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderSide:
                                  BorderSide(color: AppColors.appPrimaryColor),
                            ),
                          ),
                          style: TextStyle(color: AppColors.appBlackColor),
                        ),
                        SizedBox(
                          height: 32.h,
                        ),
                        Container(
                          height: 51.h,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(5.r),
                              color: Get.find<AppController>()
                                  .getDarkCardColorDefault(),
                              border: Border.all(
                                  color:
                                      LocalStorage.get(LocalStorage.isDark) !=
                                                  null &&
                                              LocalStorage.get(
                                                      LocalStorage.isDark) ==
                                                  true
                                          ? Colors.transparent
                                          : AppColors.appBlack10)),
                          child: Row(
                            children: [
                              Padding(
                                padding:
                                    const EdgeInsets.only(left: 8, right: 16),
                                child: GestureDetector(
                                  onTap: () {
                                    _pickFiles();
                                  },
                                  child: Container(
                                    height: 35.h,
                                    width: 114.w,
                                    decoration: BoxDecoration(
                                      color: LocalStorage.get(
                                                      LocalStorage.isDark) !=
                                                  null &&
                                              LocalStorage.get(
                                                      LocalStorage.isDark) ==
                                                  true
                                          ? Color(0xff0E1621)
                                          : AppColors.appPrimaryColor30,
                                      borderRadius: BorderRadius.circular(5),
                                    ),
                                    child: Center(
                                      child: Text(
                                        storedLanguage['Choose Files'] ??
                                            "Choose Files",
                                        style: TextStyle(
                                            color: AppColors.appBlack70,
                                            fontSize: 16.sp),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              Center(
                                  child: Text(
                                _fileNames.isNotEmpty
                                    ? "${_fileNames.length} ${storedLanguage['File Selected'] ?? "File Selected"}"
                                    : storedLanguage['No File Selected'] ??
                                        "No File Selected",
                                style: TextStyle(
                                    color: AppColors.appBlack70,
                                    fontSize: 16.sp),
                              )),
                            ],
                          ),
                        ),
                        SizedBox(
                          height: 32.h,
                        ),
                        GestureDetector(
                          onTap: () async {
                            Helpers.hideKeyboard();
                            if (createTicketController
                                .subjectCtrl.text.isEmpty) {
                              Helpers.showSnackBar(
                                  msg: "Subject Field is required.");
                            } else if (createTicketController
                                .messageCtrl.text.isEmpty) {
                              Helpers.showSnackBar(
                                  msg: "Message Field is required.");
                            } else {
                              await createTicketController
                                  .createTicketRequest(
                                createTicketController.subjectCtrl.text
                                    .toString(),
                                createTicketController.messageCtrl.text
                                    .toString(),
                                selectedFilePaths,
                              )
                                  .then((value) async {
                                Navigator.pop(context);
                                await Get.find<TicketListController>()
                                    .getTicketListData(page: 1);

                                // after creating a new ticket then reset the data
                                createTicketController.subjectCtrl.clear();
                                createTicketController.messageCtrl.clear();
                                selectedFilePaths.clear();
                              });
                            }
                          },
                          child: Container(
                            height: 45.h,
                            width: 326.w,
                            decoration: BoxDecoration(
                                color: AppColors.appPrimaryColor,
                                borderRadius: BorderRadius.circular(4)),
                            child: Center(
                              child: createTicketController.isLoading == false
                                  ? Text(
                                      "Submit",
                                      style: TextStyle(
                                          fontSize: 18.sp,
                                          color: AppColors.appWhiteColor,
                                          fontWeight: FontWeight.w500),
                                    )
                                  : CircularProgressIndicator(
                                      color: AppColors.appWhiteColor,
                                    ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            )
          ],
        ),
      );
    });
  }
}
