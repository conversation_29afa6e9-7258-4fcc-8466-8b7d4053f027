import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:Nestblock/controller/two_fa_controller.dart';

import '../../../controller/app_controller.dart';
import '../../../utils/app_colors.dart';
import '../../../utils/local_storage.dart';
import '../../widgets/flexible_space_widget.dart';

class TwoFaScreen extends StatelessWidget {
  final bool? isTwofaVerification;
  static const String routeName = "/twoFaScreen";
  TwoFaScreen({super.key, this.isTwofaVerification = false});

  final GlobalKey<FormState> _formKeyEnable = GlobalKey<FormState>();
  final GlobalKey<FormState> _formKeyDisable = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    var storedLanguage = LocalStorage.get(LocalStorage.languageData) ?? {};
    return GetBuilder<TwoFaSecurityController>(
        builder: (twoFaSecurityController) {
      return Scaffold(
        backgroundColor: AppColors.appPrimaryColor,
        appBar: buildAppBar(context, storedLanguage),
        body: Container(
          decoration: BoxDecoration(
            color: Get.find<AppController>().getDarkBgColor(),
            borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
          ),
          child: ListView(
            children: [
              Padding(
                padding: EdgeInsets.only(top: 20.h),
                child: Image.asset(
                  "assets/images/security.png",
                  height: 148.h,
                  width: 148.w,
                ),
              ),
              SizedBox(
                height: 60.h,
              ),
              twoFaSecurityController.isLoading == false
                  ? Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 32),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            storedLanguage['Two Factor Authentication'] ??
                                "Two Factor Authenticator",
                            style: TextStyle(
                                fontSize: 18.sp,
                                color: AppColors.appBlackColor,
                                fontWeight: FontWeight.w500),
                          ),
                          SizedBox(
                            height: 12.h,
                          ),
                          Stack(
                            children: [
                              Container(
                                height: 43.h,
                                width: double.infinity,
                                padding: EdgeInsets.only(left: 10.w),
                                alignment: Alignment.centerLeft,
                                decoration: BoxDecoration(
                                    color: Get.find<AppController>()
                                        .getDarkCardColorDefault(),
                                    borderRadius: BorderRadius.circular(5),
                                    border: Border.all(
                                        color: AppColors.appBlack10)),
                                child: Container(
                                  width: MediaQuery.sizeOf(context).width * .53,
                                  child: Text(
                                    twoFaSecurityController.message == null
                                        ? ""
                                        : "${twoFaSecurityController.message?.secret}",
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                        fontSize: 16.sp,
                                        color: AppColors.appBlack50,
                                        fontWeight: FontWeight.normal),
                                  ),
                                ),
                              ),
                              Positioned(
                                right: 0,
                                child: GestureDetector(
                                  onTap: () {
                                    Clipboard.setData(ClipboardData(
                                        text: twoFaSecurityController.message ==
                                                null
                                            ? ""
                                            : "${twoFaSecurityController.message!.secret}"));
                                    ScaffoldMessenger.of(context)
                                        .removeCurrentSnackBar();
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                          duration: const Duration(seconds: 1),
                                          backgroundColor:
                                              AppColors.appGreenColor,
                                          content: Text(
                                            'Text copied to clipboard',
                                            style: TextStyle(
                                                fontSize: 15.sp,
                                                color: Colors.white),
                                          )),
                                    );
                                  },
                                  child: Container(
                                    height: 43.h,
                                    width: 80.w,
                                    decoration: BoxDecoration(
                                        border: Border.all(
                                            color: AppColors.appBlack10)),
                                    child: Center(
                                      child: Text(
                                        storedLanguage['Copy'] ?? "Copy",
                                        style: TextStyle(
                                            fontSize: 16.sp,
                                            color: AppColors.appBlack50,
                                            fontWeight: FontWeight.normal),
                                      ),
                                    ),
                                  ),
                                ),
                              )
                            ],
                          ),
                          SizedBox(
                            height: 40.h,
                          ),
                          Center(
                            child: QrImageView(
                              foregroundColor: Get.isDarkMode
                                  ? AppColors.appWhiteColor
                                  : AppColors.appBlackColor,
                              data: twoFaSecurityController.message == null
                                  ? ""
                                  : '${twoFaSecurityController.message!.qrCodeUrl}',
                              version: QrVersions.auto,
                              size: 200.0,
                            ),
                          ),
                          SizedBox(
                            height: 40.h,
                          ),
                          GestureDetector(
                            onTap: twoFaSecurityController.message == null
                                ? null
                                : () {
                                    twoFaSecurityController.message == null
                                        ? null
                                        : twoFaSecurityController
                                                    .message!.twoFactorEnable !=
                                                true
                                            ? Get.defaultDialog(
                                                barrierDismissible: false,
                                                titlePadding:
                                                    EdgeInsets.only(top: 10.h),
                                                title: storedLanguage[
                                                        '2 Step Security'] ??
                                                    '2 Step Security',
                                                content: Padding(
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                      horizontal: 10,
                                                      vertical: 16),
                                                  child: Form(
                                                    key: _formKeyEnable,
                                                    child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      children: [
                                                        Text(
                                                          storedLanguage[
                                                                  'Verify your OTP'] ??
                                                              'Verify your OTP',
                                                          style: TextStyle(
                                                              fontSize: 14.sp,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w500),
                                                        ),
                                                        SizedBox(height: 10.h),
                                                        TextFormField(
                                                          validator: (value) {
                                                            if (value!
                                                                .isEmpty) {
                                                              return 'Code is required';
                                                            }
                                                            return null; // Return null if the input is valid.
                                                          },
                                                          keyboardType:
                                                              TextInputType
                                                                  .number,
                                                          controller:
                                                              twoFaSecurityController
                                                                  .googleAuthEnableCode,
                                                          decoration:
                                                              InputDecoration(
                                                            contentPadding:
                                                                const EdgeInsets
                                                                    .symmetric(
                                                                    vertical: 8,
                                                                    horizontal:
                                                                        8),
                                                            hintText: storedLanguage[
                                                                    'Enter Google Authentication Code'] ??
                                                                "Enter Google Authenticator Code",
                                                            hintStyle: TextStyle(
                                                                color:
                                                                    Colors.grey,
                                                                fontSize: 14.sp,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w400),
                                                            filled:
                                                                true, // Fill the background with color
                                                            fillColor: AppColors
                                                                .appInputFieldColor, // Background color
                                                            enabledBorder:
                                                                OutlineInputBorder(
                                                              borderSide: BorderSide(
                                                                  color: AppColors
                                                                      .appWhiteColor),
                                                            ),
                                                            focusedBorder:
                                                                OutlineInputBorder(
                                                              borderSide: BorderSide(
                                                                  color: AppColors
                                                                      .appPrimaryColor),
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                                cancel: ElevatedButton(
                                                  style:
                                                      ElevatedButton.styleFrom(
                                                    backgroundColor: Colors
                                                        .red, // Customize the button color
                                                  ),
                                                  onPressed: () {
                                                    Get.back(); // Close the dialog
                                                  },
                                                  child: Text(
                                                    storedLanguage['Cancel'] ??
                                                        'Cancel',
                                                    style: TextStyle(
                                                        color: Colors.white,
                                                        fontSize: 14.sp),
                                                  ),
                                                ),
                                                confirm: ElevatedButton(
                                                  style: ElevatedButton.styleFrom(
                                                      backgroundColor: AppColors
                                                          .appPrimaryColor // Customize the button color
                                                      ),
                                                  onPressed: () {
                                                    if (_formKeyEnable
                                                        .currentState!
                                                        .validate()) {
                                                      twoFaSecurityController
                                                          .enableTwoFactorSecurity(
                                                              twoFaSecurityController
                                                                  .googleAuthEnableCode
                                                                  .text
                                                                  .toString(),
                                                              twoFaSecurityController
                                                                          .message ==
                                                                      null
                                                                  ? ""
                                                                  : twoFaSecurityController
                                                                      .message!
                                                                      .secret
                                                                      .toString())
                                                          .then((value) {
                                                        twoFaSecurityController
                                                            .googleAuthEnableCode
                                                            .text = "";
                                                        twoFaSecurityController
                                                            .getTwoFaSecurityData();
                                                        Navigator.pop(context);
                                                      });
                                                    }
                                                  },
                                                  child: Text(
                                                    storedLanguage['Verify'] ??
                                                        'Verify',
                                                    style: TextStyle(
                                                        color: Colors.white,
                                                        fontSize: 14.sp),
                                                  ),
                                                ),
                                              )
                                            : Get.defaultDialog(
                                                barrierDismissible: false,
                                                titlePadding:
                                                    EdgeInsets.only(top: 10.h),
                                                title: storedLanguage[
                                                        '2 Step Security'] ??
                                                    '2 Step Security',
                                                content: Padding(
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                      horizontal: 10,
                                                      vertical: 16),
                                                  child: Form(
                                                    key: _formKeyDisable,
                                                    child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      children: [
                                                        Text(
                                                          storedLanguage[
                                                                  'Code'] ??
                                                              'Code',
                                                          style: TextStyle(
                                                              fontSize: 14.sp,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w500),
                                                        ),
                                                        SizedBox(height: 10.h),
                                                        TextFormField(
                                                          validator: (value) {
                                                            if (value!
                                                                .isEmpty) {
                                                              return 'Code is required';
                                                            }
                                                            return null; // Return null if the input is valid.
                                                          },
                                                          keyboardType:
                                                              TextInputType
                                                                  .number,
                                                          controller:
                                                              twoFaSecurityController
                                                                  .googleAuthDisableCode,
                                                          decoration:
                                                              InputDecoration(
                                                            contentPadding:
                                                                const EdgeInsets
                                                                    .symmetric(
                                                                    vertical: 8,
                                                                    horizontal:
                                                                        8),
                                                            hintText: storedLanguage[
                                                                    'Enter Google Authentication Code'] ??
                                                                "Enter Google Authenticator Code",
                                                            hintStyle: TextStyle(
                                                                color:
                                                                    Colors.grey,
                                                                fontSize: 14.sp,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w400),
                                                            filled:
                                                                true, // Fill the background with color
                                                            fillColor: AppColors
                                                                .appInputFieldColor, // Background color
                                                            enabledBorder:
                                                                OutlineInputBorder(
                                                              borderSide: BorderSide(
                                                                  color: AppColors
                                                                      .appWhiteColor),
                                                            ),
                                                            focusedBorder:
                                                                OutlineInputBorder(
                                                              borderSide: BorderSide(
                                                                  color: AppColors
                                                                      .appPrimaryColor),
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                                cancel: ElevatedButton(
                                                  style:
                                                      ElevatedButton.styleFrom(
                                                    backgroundColor: Colors
                                                        .red, // Customize the button color
                                                  ),
                                                  onPressed: () {
                                                    Get.back(); // Close the dialog
                                                  },
                                                  child: Text(
                                                    storedLanguage['Cancel'] ??
                                                        'Cancel',
                                                    style: TextStyle(
                                                        color: Colors.white,
                                                        fontSize: 14.sp),
                                                  ),
                                                ),
                                                confirm: ElevatedButton(
                                                  style: ElevatedButton.styleFrom(
                                                      backgroundColor: AppColors
                                                          .appPrimaryColor // Customize the button color
                                                      ),
                                                  onPressed: () {
                                                    if (_formKeyDisable
                                                        .currentState!
                                                        .validate()) {
                                                      twoFaSecurityController
                                                          .disableTwoFactorSecurity(
                                                        twoFaSecurityController
                                                            .googleAuthDisableCode
                                                            .text
                                                            .toString(),
                                                      )
                                                          .then((value) {
                                                        twoFaSecurityController
                                                            .googleAuthDisableCode
                                                            .text = "";
                                                        twoFaSecurityController
                                                            .getTwoFaSecurityData();
                                                        Navigator.pop(context);
                                                      });
                                                    }
                                                  },
                                                  child: Text(
                                                    storedLanguage['Disable'] ??
                                                        'Disable',
                                                    style: TextStyle(
                                                        color: Colors.white,
                                                        fontSize: 14.sp),
                                                  ),
                                                ),
                                              );
                                  },
                            child: Container(
                              height: 45.h,
                              width: 326.w,
                              decoration: BoxDecoration(
                                  color: AppColors.appPrimaryColor,
                                  borderRadius: BorderRadius.circular(4)),
                              child: Center(
                                child: Text(
                                  twoFaSecurityController.message == null
                                      ? ""
                                      : twoFaSecurityController
                                                  .message!.twoFactorEnable !=
                                              true
                                          ? storedLanguage[
                                                  'Enable Two Factor'] ??
                                              "Enable Two Factor"
                                          : storedLanguage[
                                                  'Disable Two Factor'] ??
                                              "Disable Two Factor",
                                  style: TextStyle(
                                      fontSize: 18.sp,
                                      color: AppColors.appWhiteColor,
                                      fontWeight: FontWeight.w500),
                                ),
                              ),
                            ),
                          ),
                          SizedBox(
                            height: 24.h,
                          ),
                        ],
                      ),
                    )
                  : const SizedBox.shrink(),
            ],
          ),
        ),
      );
    });
  }

  PreferredSize buildAppBar(BuildContext context, storedLanguage) {
    return PreferredSize(
      preferredSize: Size.fromHeight(60.h),
      child: AppBar(
        centerTitle: true,
        backgroundColor: AppColors.appPrimaryColor,
        leading: GestureDetector(
          onTap: () {
            Navigator.pop(context);
          },
          child: Icon(
            Icons.arrow_back,
            color: AppColors.appWhiteColor,
          ),
        ),
        automaticallyImplyLeading: false,
        titleSpacing: 5,
        elevation: 1,
        flexibleSpace: FlexibleSpaceWidget(),
        title: Text(
          storedLanguage['2Fa Security'] ?? "2FA Security",
          style: TextStyle(
              fontSize: 24.sp,
              color: AppColors.appWhiteColor,
              fontWeight: FontWeight.w500),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}
