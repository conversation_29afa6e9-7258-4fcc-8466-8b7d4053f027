import 'package:google_fonts/google_fonts.dart';
import 'package:Nestblock/routes/page_index.dart';
import 'package:Nestblock/utils/helpers.dart';
import 'package:Nestblock/utils/local_storage.dart';
import '../../../controller/app_controller.dart';
import '../../../di_container.dart';
import '../../../utils/app_colors.dart';

class LanguageScreen extends StatefulWidget {
  static const String routeName = "/languageScreen";
  const LanguageScreen({super.key});

  @override
  State<LanguageScreen> createState() => _LanguageScreenState();
}

class _LanguageScreenState extends State<LanguageScreen> {
  dynamic selectedLanguage;
  var appControll =
      Get.put<AppController>(AppController(appControllerRepo: sl()));
  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      appControll.getLanguagesList();
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AppController>(builder: (languageController) {
      var storedLanguage = LocalStorage.get(LocalStorage.languageData) ?? {};
      return Scaffold(
        backgroundColor: Get.find<AppController>().getDarkBgColor(),
        appBar: AppBar(
          centerTitle: true,
          backgroundColor: Get.find<AppController>().getDarkCardColorDefault(),
          titleSpacing: 0,
          leading: GestureDetector(
            onTap: () {
              Navigator.pop(context);
            },
            child: Icon(Icons.arrow_back,
                size: 25.h, color: AppColors.appBlackColor),
          ),
          automaticallyImplyLeading: false,
          title: Text(
            "${storedLanguage['Select Language'] ?? "Select Language"}",
            style: GoogleFonts.publicSans(
                fontWeight: FontWeight.w600,
                fontSize: 20.sp,
                color: AppColors.appBlackColor),
          ),
        ),
        body: languageController.isLoading == false
            ? Padding(
                padding: const EdgeInsets.symmetric(vertical: 5),
                child: ListView.builder(
                  itemCount: languageController.languageNameList.length,
                  itemBuilder: (BuildContext context, int index) {
                    final language = languageController.languageNameList[index];
                    final bool isSelected = language.name ==
                        LocalStorage.get(LocalStorage.languageName);

                    return Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 5),
                      child: Container(
                        padding: const EdgeInsets.all(5),
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: languageController.getLanguageCardColor(),
                          borderRadius: BorderRadius.circular(5),
                        ),
                        child: Column(
                          children: [
                            ListTile(
                                dense: true,
                                title: Text(
                                  language.name.toString(),
                                  style: TextStyle(
                                    fontSize: 14.sp,
                                    color: AppColors.appBlackColor,
                                  ),
                                ),
                                trailing: isSelected
                                    ? const Icon(Icons.check,
                                        color: Colors.green)
                                    : LocalStorage.get(LocalStorage
                                                    .languageName) ==
                                                null &&
                                            language.shortName == "en"
                                        ? const Icon(Icons.check,
                                            color: Colors.green)
                                        : null,
                                onTap: () {
                                  setState(() {
                                    selectedLanguage = language.name;
                                    LocalStorage.write(
                                        LocalStorage.languageName,
                                        selectedLanguage);
                                    LocalStorage.get(LocalStorage.languageData);
                                    languageController.update();
                                    languageController
                                        .getLanguagesDataById(
                                            id: language.id!.toString())
                                        .then((value) {
                                      languageController.getLanguagesList();
                                    });
                                  });
                                }),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              )
            : Center(
                child: Helpers.appLoader(),
              ),
      );
    });
  }
}
