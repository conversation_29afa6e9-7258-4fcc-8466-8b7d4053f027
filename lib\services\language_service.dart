import 'package:Nestblock/utils/local_storage.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class LanguageService extends GetxService {
  late Locale _locale;

  Locale get locale => _locale;

  // Initialize service and set the locale
  Future<LanguageService> init() async {
    // Get saved language from local storage, default to English if not set
    String language =
        LocalStorage.getLanguage() ?? Get.deviceLocale?.languageCode;
    if (language == 'ar') {
      _locale = Locale('ar', 'AR');
    } else {
      _locale = Locale('en', 'US');
    }
    return this;
  }
}
