import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:Nestblock/utils/app_colors.dart';
import 'package:Nestblock/utils/helpers.dart';
import '../../../controller/app_controller.dart';
import '../../../controller/verification_controller.dart';
import '../../../utils/local_storage.dart';
import '../../widgets/flexible_space_widget.dart';

class AddressVerificationScreen extends StatefulWidget {
  static const String routeName = "/addressVerificationScreen";
  const AddressVerificationScreen({super.key});

  @override
  State<AddressVerificationScreen> createState() =>
      _AddressVerificationScreenState();
}

class _AddressVerificationScreenState extends State<AddressVerificationScreen> {
  String imagePath = "";
  String imageName = "";
  XFile? pickedImageFile;

  Future<void> _pickFiles() async {
    final storageStatus = await Permission.camera.request();

    if (storageStatus.isGranted) {
      try {
        final picker = ImagePicker();
        pickedImageFile = await picker.pickImage(source: ImageSource.gallery);

        setState(() {
          imagePath = pickedImageFile!.path;
          imageName = pickedImageFile!.name;
        });
      } catch (e) {
        if (kDebugMode) {
          print("Error while picking files: $e");
        }
      }
    } else {
      Helpers.showSnackBar(
          msg:
              "Please grant camera permission in app settings to use this feature.");
    }
  }

  @override
  Widget build(BuildContext context) {
    var storedLanguage = LocalStorage.get(LocalStorage.languageData) ?? {};
    Get.find<VerificationController>().getVerification();
    return Scaffold(
      backgroundColor: AppColors.appPrimaryColor,
      appBar: buildAppBar(context, storedLanguage),
      body: Container(
        decoration: BoxDecoration(
          color: Get.find<AppController>().getDarkBgColor(),
          borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
        ),
        child: ListView(
          children: [
            Obx(() => Get.find<VerificationController>().isLoading
                ? Padding(
                    padding: EdgeInsets.all(28.0.h),
                    child: Center(child: Helpers.appLoader()),
                  )
                : Get.find<VerificationController>().isAddressVerified
                    ? Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(height: 20.h),
                          Align(
                            alignment: Alignment.center,
                            child: Container(
                              padding: Get.find<VerificationController>()
                                      .addressMessage
                                      .toString()
                                      .contains("pending")
                                  ? EdgeInsets.all(20.h)
                                  : EdgeInsets.zero,
                              decoration: BoxDecoration(
                                color: Get.find<VerificationController>()
                                        .addressMessage
                                        .toString()
                                        .contains("pending")
                                    ? AppColors.appDashBoardTransactionPending
                                    : Colors.transparent,
                                shape: BoxShape.circle,
                              ),
                              child: Image.asset(
                                Get.find<VerificationController>()
                                        .addressMessage
                                        .toString()
                                        .contains("pending")
                                    ? 'assets/images/pending_icon_history.png'
                                    : 'assets/images/verified_icon.png',
                                height: 90.h,
                                width: 90.h,
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                          SizedBox(height: 10.h),
                          Align(
                            alignment: Alignment.center,
                            child: Text(
                              Get.find<VerificationController>().addressMessage,
                              style: TextStyle(
                                color: Get.find<VerificationController>()
                                        .addressMessage
                                        .toString()
                                        .contains("pending")
                                    ? AppColors.appDashBoardTransactionPending
                                    : AppColors.appGreenColor,
                                fontFamily: "Dubai",
                                fontSize: 18.sp,
                              ),
                            ),
                          ),
                        ],
                      )
                    : Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: 32.w, vertical: 20.h),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              storedLanguage['Address Proof'] ??
                                  "Address Proof",
                              style: TextStyle(
                                  fontSize: 18.sp,
                                  color: AppColors.appBlackColor,
                                  fontWeight: FontWeight.normal),
                            ),
                            SizedBox(
                              height: 10.h,
                            ),
                            Container(
                              height: 45.h,
                              width: double.infinity,
                              decoration: BoxDecoration(
                                  color: Get.find<AppController>()
                                      .getDarkCardColorDefault(),
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                      color: LocalStorage.get(
                                                      LocalStorage.isDark) !=
                                                  null &&
                                              LocalStorage.get(
                                                      LocalStorage.isDark) ==
                                                  true
                                          ? Colors.grey.shade800
                                          : AppColors.appBlack10)),
                              child: Row(
                                children: [
                                  MaterialButton(
                                    onPressed: () async {
                                      _pickFiles();
                                    },
                                    child: Text(
                                      storedLanguage['Choose Files'] ??
                                          "Choose File",
                                      style: TextStyle(
                                          color: AppColors.appBlack70,
                                          fontSize: 14.sp),
                                    ),
                                  ),
                                  SizedBox(width: 5.w),
                                  Container(
                                    width: 1,
                                    color:
                                        LocalStorage.get(LocalStorage.isDark) !=
                                                    null &&
                                                LocalStorage.get(
                                                        LocalStorage.isDark) ==
                                                    true
                                            ? Colors.grey.shade800
                                            : AppColors.appBlack10,
                                  ),
                                  SizedBox(width: 13.w),
                                  Text(
                                    imagePath.isNotEmpty
                                        ? "1 ${storedLanguage['File Selected'] ?? "File Selected"}"
                                        : storedLanguage['No File Selected'] ??
                                            "No File Selected",
                                    style: TextStyle(
                                        color: imagePath.isNotEmpty
                                            ? AppColors.appGreenColor
                                            : AppColors.appBlack70,
                                        fontSize: 16.sp),
                                  )
                                ],
                              ),
                            ),
                            SizedBox(
                              height: 35.h,
                            ),
                            Obx(
                              () => GestureDetector(
                                onTap: Get.find<VerificationController>()
                                        .isLoading
                                    ? null
                                    : () async {
                                        if (imagePath.isEmpty) {
                                          // Helpers().showValidationErrorDialog(
                                          //     title: "Error",
                                          //     errorText:
                                          //         "Please Select a file first");
                                          Helpers.showSnackBar(
                                            msg: "Please Select a file first",
                                          );
                                        } else {
                                          await Get.find<
                                                  VerificationController>()
                                              .addressVerify(
                                                  filePath: imagePath,
                                                  fileName: imageName);
                                        }
                                      },
                                child: Container(
                                  height: 40.h,
                                  width: 326.w,
                                  decoration: BoxDecoration(
                                      color: AppColors.appPrimaryColor,
                                      borderRadius: BorderRadius.circular(4)),
                                  child: Center(
                                    child: Get.find<VerificationController>()
                                            .isLoading
                                        ? Helpers.appLoader(color: Colors.white)
                                        : Text(
                                            storedLanguage['Submit'] ??
                                                "Submit",
                                            style: TextStyle(
                                                fontSize: 20.sp,
                                                color: AppColors.appWhiteColor,
                                                fontWeight: FontWeight.w500),
                                          ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      )),
          ],
        ),
      ),
    );
  }

  PreferredSize buildAppBar(BuildContext context, storedLanguage) {
    return PreferredSize(
      preferredSize: Size.fromHeight(60.h),
      child: AppBar(
        centerTitle: true,
        leading: GestureDetector(
          onTap: () {
            Navigator.pop(context);
          },
          child: Icon(
            Icons.arrow_back,
            color: AppColors.appWhiteColor,
          ),
        ),
        automaticallyImplyLeading: false,
        titleSpacing: 5,
        elevation: 1,
        flexibleSpace: FlexibleSpaceWidget(),
        title: Text(
          storedLanguage['Address Verification'] ?? "Address Verification",
          style: TextStyle(
              fontSize: 24.sp,
              color: AppColors.appWhiteColor,
              fontWeight: FontWeight.w500),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}
