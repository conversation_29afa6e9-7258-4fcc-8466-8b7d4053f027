import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:open_file/open_file.dart';
import 'package:pdf/pdf.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:printing/printing.dart';
import 'package:Nestblock/routes/page_index.dart';
import 'package:Nestblock/utils/helpers.dart';

class PdfHelper {
  Future<void> saveAsFile({
    BuildContext? context,
    LayoutCallback? build,
    PdfPageFormat? pageFormat,
  }) async {
    try {
      if (await Permission.storage.request().isGranted) {
        // Get the downloads directory
        final directory = await getExternalStorageDirectory();
        if (directory == null) {
          throw Exception('Could not access external storage');
        }

        // Create a downloads folder if it doesn't exist
        final downloadsPath = path.join(directory.path, 'Download');
        final dir = Directory(downloadsPath);
        if (!await dir.exists()) {
          await dir.create(recursive: true);
        }

        // Generate PDF bytes
        final bytes = await build!(pageFormat!);

        // Create the file path
        final fileName = 'transfer_log_invoice.pdf';
        final filePath = path.join(downloadsPath, fileName);
        final file = File(filePath);

        // Write the PDF file
        await file.writeAsBytes(bytes);

        if (context != null) {
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
              content: Text("File saved successfully in Download folder")));
        }

        // Open the file
        await OpenFile.open(filePath);
      } else {
        if (context != null) {
          Helpers.showSnackBar(
              msg: "Need Storage permission for downloading this receipt",
              bgColor: AppColors.appGreenColor);
        }
      }
    } catch (e) {
      print('Error saving PDF: $e');
      if (context != null) {
        ScaffoldMessenger.of(context)
            .showSnackBar(SnackBar(content: Text("Error saving PDF: $e")));
      }
    }
  }
}
