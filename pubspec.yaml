name: Nestblock
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ">=3.4.0 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  # flutter build apk --split-per-abi
  cupertino_icons: ^1.0.2
  # to responsive ui design
  flutter_screenutil: ^5.9.3
  google_fonts:
  # state management 
  get: ^4.7.2
  get_it: ^8.0.3
  dio: ^5.6.0
  shared_preferences: ^2.2.0
  pin_code_fields: ^8.0.1
  qr_flutter: ^4.1.0
  cached_network_image: ^3.3.0
  dropdown_button2: ^2.3.9
  country_code_picker: ^3.3.0
  intl: ^0.20.2
  image_picker: ^1.0.2
  shimmer: ^3.0.0
  file_picker: ^9.2.1
  permission_handler: ^11.4.0
  fluttertoast: ^8.2.4
  path_provider: ^2.1.1
  path: ^1.8.3
  # pdf generator
  pdf: ^3.11.3
  printing: ^5.14.2
  open_file: ^3.3.2  
  #local notification
  flutter_local_notifications: ^19.0.0
  pusher_channels_flutter: ^2.5.0
  # local storage
  get_storage: ^2.1.1
  dotted_border: ^2.1.0 
  http: ^1.2.2
  flutter_inappwebview: ^6.1.5
  lottie: ^3.0.0
  #Payment Gateway
  flutter_stripe: ^11.4.0
  # razorpay_flutter: ^1.4.0
  monnify_payment_sdk: ^1.0.4
  # paytmpayments_allinonesdk: ^1.0.0
  flutter_paypal_payment: ^1.0.8
  webview_flutter_android: ^4.3.3
  # flutter_paystack: 
  flutterwave_standard_smart: ^1.0.4
  # paytm: 
  # change_app_name and package_name
  # flutter pub run change_app_package_name:main com.new.package.name
  # flutter pub run rename_app:main android="Anroid Name" ios="IOS Name" others="Others Name"
  change_app_package_name:
  rename_app: ^1.3.1
  #change app icon
  flutter_launcher_icons: ^0.14.3
  # check internet
  connectivity_plus: ^6.1.3
  logger: ^2.1.0

  # localization
  flutter_localization: ^0.3.1


dev_dependencies:
  flutter_test:
    sdk: flutter

dependency_overrides:
  archive: ^4.0.4
#change_app_icon
flutter_launcher_icons:
  android: true
  ios: true
  remove_alpha_ios: true
  image_path: "assets/icon/app_icon.png"
#flutter pub run flutter_launcher_icons:main

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  # flutter_lints: ^2.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Dubai
      fonts:
        - asset: assets/fonts/Dubai-Bold.ttf
        - asset: assets/fonts/Dubai-Light.ttf
        - asset: assets/fonts/Dubai-Medium.ttf
        - asset: assets/fonts/Dubai-Regular.ttf
        
    - family: Cairo
      fonts:
        - asset: assets/fonts/cairo/Cairo-VariableFont_slnt,wght.ttf
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
