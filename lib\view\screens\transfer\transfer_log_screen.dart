import 'package:Nestblock/data/model/response/transfer_log_model.dart';
import 'package:intl/intl.dart';
import '../../../controller/app_controller.dart';
import '../../../controller/money_calculation_proceed_controller.dart';
import '../../../controller/transfer_log_controller.dart';
import '../../../di_container.dart';
import '../../../routes/page_index.dart';
import '../../../utils/app_colors.dart';
import '../../../utils/helpers.dart';
import '../../../utils/local_storage.dart';
import '../../widgets/app_dialog.dart';
import '../../widgets/flexible_space_widget.dart';
import '../landing/bottom_navbar.dart';
import '../landing/payment/payment_method_screen.dart';

class TransferLogScreen extends StatefulWidget {
  final dynamic status;
  static const String routeName = "/transferLogScreen";
  const TransferLogScreen({super.key, this.status = false});

  @override
  State<TransferLogScreen> createState() => _TransferLogScreenState();
}

class _TransferLogScreenState extends State<TransferLogScreen> {
  @override
  Widget build(BuildContext context) {
    var storedLanguage = LocalStorage.get(LocalStorage.languageData) ?? {};
    var paymentGatewayController =
        Get.put(PaymentGatewayController(paymentGatewayRepo: sl()));
    var moneyCalculationProceedController = Get.put(
        MoneyCalculationProceedController(moneyCalculationProceedRepo: sl()));

    return GetBuilder<TransferLogController>(builder: (transferLogController) {
      return WillPopScope(
        onWillPop: () async {
          if (widget.status == true) {
            Get.offAllNamed(BottomNavBar.routeName);
          } else {
            Navigator.pop(context);
          }
          return true;
        },
        child: Scaffold(
          backgroundColor: AppColors.appPrimaryColor,
          appBar: buildAppbar(context, storedLanguage, transferLogController),
          body: RefreshIndicator(
            onRefresh: () async {
              transferLogController.resetData(isFromOnRefreshIndicator: true);
              await transferLogController.getTransferLogHistory(
                  invoice: "",
                  send_amount: "",
                  receive_amount: "",
                  paid_at: "",
                  page: 1);
            },
            child: Container(
              height: transferLogController.isLoading == true ||
                      transferLogController.transferLogList.isEmpty
                  ? MediaQuery.sizeOf(context).height
                  : MediaQuery.sizeOf(context).height,
              decoration: BoxDecoration(
                color: Get.find<AppController>().getDarkBgColor(),
                borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
              ),
              child: SingleChildScrollView(
                controller: transferLogController.scrollController,
                physics: const AlwaysScrollableScrollPhysics(),
                child: Column(
                  mainAxisAlignment:
                      transferLogController.transferLogList.isEmpty
                          ? MainAxisAlignment.center
                          : MainAxisAlignment.start,
                  children: [
                    transferLogController.isLoading == false
                        ? transferLogController.transferLogList.isEmpty
                            ? SizedBox(
                                height: MediaQuery.sizeOf(context).height * .6,
                                child: Center(
                                    child: Image.asset(
                                  "assets/images/not_found.png",
                                  fit: BoxFit.cover,
                                  height: 150.h,
                                )),
                              )
                            : ListView.builder(
                                itemCount: transferLogController
                                    .transferLogList.length,
                                shrinkWrap: true,
                                padding: EdgeInsets.only(top: 20.h),
                                physics: NeverScrollableScrollPhysics(),
                                itemBuilder: (context, index) {
                                  var data = transferLogController
                                      .transferLogList[index];
                                  return Padding(
                                    padding: EdgeInsets.only(
                                        bottom: 16.h, right: 20.w, left: 20.w),
                                    child: GestureDetector(
                                      onTap: () {
                                        openDialog(
                                            context,
                                            transferLogController,
                                            data,
                                            storedLanguage,
                                            paymentGatewayController,
                                            moneyCalculationProceedController);
                                      },
                                      child: Container(
                                        width: double.infinity,
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(5),
                                          color: Get.find<AppController>()
                                              .getDarkCardColorDefault(),
                                          boxShadow: LocalStorage.get(
                                                          LocalStorage
                                                              .isDark) !=
                                                      null &&
                                                  LocalStorage.get(LocalStorage
                                                          .isDark) ==
                                                      true
                                              ? []
                                              : [
                                                  BoxShadow(
                                                    color: Get.find<
                                                            AppController>()
                                                        .getLanguageCardColor(),
                                                    offset: const Offset(2, 2),
                                                    blurRadius: 4,
                                                    spreadRadius: 2,
                                                  ),
                                                ],
                                        ),
                                        child: Stack(
                                          children: [
                                            Column(
                                              children: [
                                                SizedBox(
                                                  width: 12.w,
                                                ),
                                                Padding(
                                                  padding: EdgeInsets.symmetric(
                                                      horizontal: 12.w,
                                                      vertical: 6.h),
                                                  child: Row(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    children: [
                                                      Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          Text(
                                                            storedLanguage[
                                                                    "Recipient"] ??
                                                                "Recipient",
                                                            style: TextStyle(
                                                                fontSize: 16.sp,
                                                                fontFamily:
                                                                    'Dubai',
                                                                color: AppColors
                                                                    .appBlackColor,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w500,
                                                                height: 1.2),
                                                          ),
                                                          Text(
                                                            "${data.recipient}",
                                                            style: TextStyle(
                                                              fontSize: 15.sp,
                                                              fontFamily:
                                                                  'Dubai',
                                                              color: AppColors
                                                                  .appBlackColor,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .normal,
                                                              height: 1.4,
                                                              //decoration: TextDecoration.underline
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                      Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .end,
                                                        children: [
                                                          Text(
                                                            storedLanguage[
                                                                    'Status'] ??
                                                                "Status",
                                                            style: TextStyle(
                                                                fontSize: 16.sp,
                                                                fontFamily:
                                                                    'Dubai',
                                                                color: AppColors
                                                                    .appBlackColor,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w500,
                                                                height: 1.2),
                                                          ),
                                                          SizedBox(height: 4.h),
                                                          Container(
                                                            padding: EdgeInsets
                                                                .symmetric(
                                                                    horizontal:
                                                                        8.w,
                                                                    vertical:
                                                                        0.h),
                                                            decoration:
                                                                BoxDecoration(
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          5),
                                                              color: checkStatusColor(
                                                                  data.status),
                                                            ),
                                                            child: Padding(
                                                              padding:
                                                                  const EdgeInsets
                                                                      .all(4.0),
                                                              child: Text(
                                                                "${data.status}",
                                                                style: TextStyle(
                                                                    fontSize:
                                                                        14.sp,
                                                                    fontFamily:
                                                                        'Dubai',
                                                                    color: checkFontColorColor(data
                                                                        .status),
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .w400,
                                                                    height:
                                                                        1.2),
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                                // Divider(
                                                //   color: AppColors.appBlack10,
                                                // ),
                                                Padding(
                                                  padding: EdgeInsets.symmetric(
                                                      horizontal: 12.w,
                                                      vertical: 6.h),
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    children: [
                                                      Row(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .spaceBetween,
                                                        children: [
                                                          Text(
                                                            storedLanguage[
                                                                    'Send Amount'] ??
                                                                "Send Amount",
                                                            style: TextStyle(
                                                                fontSize: 15.sp,
                                                                fontFamily:
                                                                    'Dubai',
                                                                color: AppColors
                                                                    .appBlackColor,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w500,
                                                                height: 1.2),
                                                          ),
                                                          Text(
                                                            "${data.sendAmount}",
                                                            style: TextStyle(
                                                                fontSize: 15.sp,
                                                                fontFamily:
                                                                    'Dubai',
                                                                color: AppColors
                                                                    .appBlackColor,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .normal,
                                                                height: 1.2),
                                                          ),
                                                        ],
                                                      ),
                                                      SizedBox(
                                                        height: 5.h,
                                                      ),
                                                      Row(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .spaceBetween,
                                                        children: [
                                                          Text(
                                                            storedLanguage[
                                                                    'Receive Amount'] ??
                                                                "Receive amount",
                                                            style: TextStyle(
                                                                fontSize: 15.sp,
                                                                fontFamily:
                                                                    'Dubai',
                                                                color: AppColors
                                                                    .appBlackColor,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w500,
                                                                height: 1.2),
                                                          ),
                                                          Text(
                                                            "${data.receiveAmount}",
                                                            style: TextStyle(
                                                                fontSize: 15.sp,
                                                                fontFamily:
                                                                    'Dubai',
                                                                color: AppColors
                                                                    .appBlackColor,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .normal,
                                                                height: 1.2),
                                                          ),
                                                        ],
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  );
                                })
                        : Container(
                            margin: EdgeInsets.only(top: 20.h),
                            child: Helpers.appLoader(),
                          ),
                    if (transferLogController.isLoadMore == true)
                      Padding(
                          padding: EdgeInsets.only(top: 10.h, bottom: 40.h),
                          child: Helpers.appLoader()),
                  ],
                ),
              ),
            ),
          ),
        ),
      );
    });
  }

  PreferredSize buildAppbar(
      BuildContext context, storedLanguage, TransferLogController controller) {
    return PreferredSize(
      preferredSize: Size.fromHeight(60.h),
      child: AppBar(
        centerTitle: true,
        backgroundColor: AppColors.appPrimaryColor,
        leading: GestureDetector(
          onTap: () {
            if (widget.status == true) {
              Get.offAllNamed(BottomNavBar.routeName);
            } else {
              Navigator.pop(context);
            }
          },
          child: Icon(
            Icons.arrow_back,
            color: AppColors.appWhiteColor,
          ),
        ),
        automaticallyImplyLeading: false,
        titleSpacing: 5,
        elevation: 1,
        flexibleSpace: FlexibleSpaceWidget(),
        title: Text(
          storedLanguage['Transfer Log'] ?? "Transfer Log",
          style: TextStyle(
              fontSize: 24.sp,
              color: AppColors.appWhiteColor,
              fontWeight: FontWeight.w500),
          textAlign: TextAlign.center,
        ),
        actions: [
          InkWell(
            onTap: () {
              appDialog(
                  isTransferLogSearch: true,
                  firstFieldName:
                      storedLanguage['Invoice Number'] ?? "Invoice Number",
                  secondFieldName:
                      storedLanguage['Send Amount'] ?? "Send Amount",
                  receiveAmountName:
                      storedLanguage['Receive Amount'] ?? "Receive Amount",
                  thirdFieldName: storedLanguage['Date Time'] ?? "Date Time",
                  onCancelTap: () async {
                    controller.resetData();

                    Get.back();
                    await controller
                        .getTransferLogHistory(
                      invoice: "",
                      send_amount: "",
                      receive_amount: "",
                      paid_at: "",
                      page: 1,
                    )
                        .then((value) {
                      controller.invoiceNumberField.clear();
                      controller.sendAmountField.clear();
                      controller.receiveAmountField.clear();
                      controller.dateTimeEditingCtrlr.clear();
                    });
                  },
                  onSearchTap: () async {
                    controller.resetData();

                    await controller
                        .getTransferLogHistory(
                      invoice: controller.invoiceNumberField.text,
                      send_amount: controller.sendAmountField.text,
                      receive_amount: controller.receiveAmountField.text,
                      paid_at: controller.dateTimeEditingCtrlr.text,
                      page: 1,
                    )
                        .then((value) {
                      controller.dateTimeEditingCtrlr.clear();
                      Get.back();
                    });
                  },
                  onDateTimeTap: () async {
                    /// SHOW DATE PICKER
                    await showDatePicker(
                            context: context,
                            initialDate: DateTime.now(),
                            firstDate: DateTime(2000),
                            lastDate: DateTime(2025))
                        .then((value) {
                      if (value != null) {
                        print(value.toUtc());
                        controller.dateTimeEditingCtrlr.text =
                            DateFormat('yyyy-MM-dd').format(value);
                      }
                    });
                  },
                  firstFieldController: controller.invoiceNumberField,
                  secondFieldController: controller.sendAmountField,
                  receiveAmountField: controller.receiveAmountField,
                  thirdFieldController: controller.dateTimeEditingCtrlr);
            },
            child: CircleAvatar(
              backgroundColor: AppColors.appPrimaryColor50,
              radius: 15,
              child: Icon(
                Icons.search_rounded,
                color: AppColors.appWhiteColor,
                size: 15.h,
              ),
            ),
          ),
          SizedBox(width: 20.w),
        ],
      ),
    );
  }

  Future<void> openDialog(
      BuildContext context,
      TransferLogController transferLogController,
      Datum data,
      storedLanguage,
      PaymentGatewayController paymentGatewayController,
      MoneyCalculationProceedController moneyCalculationProceedController) {
    return showDialog<void>(
      barrierDismissible: true,
      context: context,
      builder: (BuildContext context) {
        return Padding(
          padding: EdgeInsets.symmetric(vertical: 60.h, horizontal: 30.w),
          child: Material(
            // Wrap with Material
            elevation: 0,
            type: MaterialType.transparency,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                      width: MediaQuery.of(context).size.width,
                      decoration: BoxDecoration(
                          color: Get.find<AppController>().getDarkBgColor(),
                          borderRadius: BorderRadius.circular(5)),
                      child: Column(
                        children: [
                          Stack(
                            children: [
                              Image.asset(
                                "assets/images/dialog_header_img.png",
                                height: 80.h,
                                width: double.infinity,
                                fit: BoxFit.cover,
                              ),
                              Positioned(
                                top: 16.h,
                                right: 16.w,
                                child: GestureDetector(
                                    onTap: () {
                                      Navigator.pop(context);
                                    },
                                    child: Image.asset(
                                      "assets/images/cancel_icon.png",
                                      height: 18.h,
                                      width: 18.w,
                                    )),
                              ),
                              Positioned(
                                top: 20.h,
                                left: 0,
                                right: 0,
                                child: Column(
                                  children: [
                                    SizedBox(
                                      height: 12.h,
                                    ),
                                    Text(
                                      storedLanguage['Transfer Log'] ??
                                          "Transfer Log",
                                      style: TextStyle(
                                          fontSize: 18.sp,
                                          color: AppColors.appWhiteColor,
                                          fontWeight: FontWeight.w500),
                                    ),
                                  ],
                                ),
                              )
                            ],
                          ),
                          Container(
                            color: Get.find<AppController>().getDarkBgColor(),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Text(
                                    storedLanguage['Invoice no'] ??
                                        "Invoice no",
                                    style: TextStyle(
                                        fontSize: 16.sp,
                                        color: AppColors.appBlack50,
                                        fontWeight: FontWeight.w500),
                                  ),
                                  InkWell(
                                    onTap: () {
                                      Clipboard.setData(ClipboardData(
                                          text: data.invoice.toString()));
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(SnackBar(
                                        backgroundColor:
                                            AppColors.appGreenColor,
                                        content: Center(
                                            child: Text(
                                          'Copied to clipboard',
                                          style: TextStyle(
                                              fontFamily: "Dubai",
                                              color: AppColors.appWhiteColor),
                                        )),
                                      ));
                                    },
                                    child: Text(
                                      "${data.invoice}",
                                      style: TextStyle(
                                          fontSize: 14.sp,
                                          color: AppColors.appBlackColor,
                                          fontWeight: FontWeight.w400),
                                    ),
                                  ),
                                  SizedBox(
                                    height: 10.h,
                                  ),
                                  Text(
                                    storedLanguage['Send Amount'] ??
                                        "Send Amount",
                                    style: TextStyle(
                                        fontSize: 16.sp,
                                        color: AppColors.appBlack50,
                                        fontWeight: FontWeight.w500),
                                  ),
                                  Text(
                                    "${data.sendAmount}",
                                    style: TextStyle(
                                        fontSize: 14.sp,
                                        color: AppColors.appBlackColor,
                                        fontWeight: FontWeight.w400),
                                  ),
                                  SizedBox(
                                    height: 10.h,
                                  ),
                                  Text(
                                    storedLanguage['Receive Amount'] ??
                                        "Receive Amount",
                                    style: TextStyle(
                                        fontSize: 16.sp,
                                        color: AppColors.appBlack50,
                                        fontWeight: FontWeight.w500),
                                  ),
                                  Text(
                                    "${data.receiveAmount}",
                                    style: TextStyle(
                                        fontSize: 14.sp,
                                        color: AppColors.appBlackColor,
                                        fontWeight: FontWeight.w400),
                                  ),
                                  SizedBox(
                                    height: 10.h,
                                  ),
                                  Text(
                                    storedLanguage['Send at'] ?? "Send at",
                                    style: TextStyle(
                                        fontSize: 16.sp,
                                        color: AppColors.appBlack50,
                                        fontWeight: FontWeight.w500),
                                  ),
                                  Text(
                                    "${data.sendAt}",
                                    style: TextStyle(
                                        fontSize: 14.sp,
                                        color: AppColors.appBlackColor,
                                        fontWeight: FontWeight.w400),
                                  ),
                                  SizedBox(
                                    height: 10.h,
                                  ),
                                  Text(
                                    storedLanguage['Receive At'] ??
                                        "Receive At",
                                    style: TextStyle(
                                        fontSize: 16.sp,
                                        color: AppColors.appBlack50,
                                        fontWeight: FontWeight.w500),
                                  ),
                                  Text(
                                    "${data.receiveAt}",
                                    style: TextStyle(
                                        fontSize: 14.sp,
                                        color: AppColors.appBlackColor,
                                        fontWeight: FontWeight.w400),
                                  ),
                                  SizedBox(
                                    height: 10.h,
                                  ),
                                  Text(
                                    storedLanguage['Rate'] ?? "Rate",
                                    style: TextStyle(
                                        fontSize: 16.sp,
                                        color: AppColors.appBlack50,
                                        fontWeight: FontWeight.w500),
                                  ),
                                  Text(
                                    "${data.rate}",
                                    style: TextStyle(
                                        fontSize: 14.sp,
                                        color: AppColors.appBlackColor,
                                        fontWeight: FontWeight.w400),
                                  ),
                                  SizedBox(
                                    height: 20.h,
                                  ),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceEvenly,
                                    children: [
                                      if (data.status!.toLowerCase() ==
                                          "information need") ...[
                                        InkWell(
                                          onTap: () async {
                                            moneyCalculationProceedController
                                                    .invoiceNumber.value =
                                                data.invoice.toString();
                                            paymentGatewayController
                                                    .totalBaseAmountPay =
                                                double.parse(data.sendAmount!
                                                        .split(' ')
                                                        .first)
                                                    .toStringAsFixed(2);
                                            paymentGatewayController
                                                .amountTextEditingCtrlr
                                                .text = double.parse(data
                                                    .sendAmount!
                                                    .split(' ')
                                                    .first)
                                                .toStringAsFixed(2);

                                            paymentGatewayController
                                                    .sendCurrency =
                                                data.sendAmount!
                                                    .split(' ')
                                                    .last;
                                            await moneyCalculationProceedController
                                                .getTransferFormData(
                                                    invoice: data.invoice
                                                        .toString());
                                            Get.delete<TransferLogController>();
                                          },
                                          child: Container(
                                              padding: EdgeInsets.all(8),
                                              height: 40.h,
                                              width: 60.w,
                                              decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.circular(5),
                                                  border: Border.all(
                                                      color: AppColors
                                                          .appBlack10)),
                                              child: Image.asset(
                                                "assets/images/edit.png",
                                                color: AppColors.appGreenColor,
                                              )),
                                        ),
                                      ],
                                      if (data.status!.toLowerCase() ==
                                          'please pay') ...[
                                        InkWell(
                                          onTap: () {
                                            moneyCalculationProceedController
                                                    .invoiceNumber.value =
                                                data.invoice.toString();
                                            paymentGatewayController
                                                    .totalBaseAmountPay =
                                                data.sendAmount!
                                                    .split(' ')
                                                    .first;
                                            paymentGatewayController
                                                    .amountTextEditingCtrlr
                                                    .text =
                                                data.sendAmount!
                                                    .split(' ')
                                                    .first;
                                            paymentGatewayController
                                                    .sendCurrency =
                                                data.sendAmount!
                                                    .split(' ')
                                                    .last;

                                            Get.back();
                                            Get.to(() => PaymentMethodScreen());

                                            Get.delete<TransferLogController>();
                                          },
                                          child: Container(
                                              padding: EdgeInsets.all(8),
                                              height: 40.h,
                                              width: 60.w,
                                              decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.circular(5),
                                                  border: Border.all(
                                                      color: AppColors
                                                          .appBlack10)),
                                              child: Image.asset(
                                                "assets/images/pay.png",
                                                color: AppColors.appGreenColor,
                                              )),
                                        ),
                                      ],
                                      if (data.status!.toLowerCase() !=
                                          "information need") ...[
                                        InkWell(
                                          onTap: () async {
                                            await transferLogController
                                                .getInvoiceInfo(
                                                    invoice:
                                                        data.invoice.toString(),
                                                    context: context);
                                          },
                                          child: Container(
                                              padding: const EdgeInsets.all(8),
                                              height: 40.h,
                                              width: 60.w,
                                              decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.circular(5),
                                                  border: Border.all(
                                                      color: AppColors
                                                          .appBlack10)),
                                              child: Image.asset(
                                                "assets/images/download.png",
                                                color:
                                                    AppColors.appPrimaryColor,
                                              )),
                                        ),
                                      ],
                                      if (data.status!.toLowerCase() !=
                                          "processing") ...[
                                        InkWell(
                                          onTap: () async {
                                            transferLogController.resetData(
                                                isFromOnRefreshIndicator: true);
                                            await transferLogController
                                                .deleteTransferLogHistory(
                                                    invoice: data.invoice
                                                        .toString());
                                          },
                                          child: Container(
                                              padding: const EdgeInsets.all(8),
                                              height: 40.h,
                                              width: 60.w,
                                              decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.circular(5),
                                                  border: Border.all(
                                                      color: AppColors
                                                          .appBlack10)),
                                              child: Image.asset(
                                                "assets/images/delete.png",
                                                color: AppColors.appRedColor,
                                              )),
                                        ),
                                      ],
                                    ],
                                  ),
                                  SizedBox(
                                    height: 15.h,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ))
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  checkStatusColor(dynamic status) {
    if (status == "Please Pay") {
      return AppColors.appPrimaryColor;
    } else if (status == "Information Need") {
      return Color(0xffFF6F37);
    } else if (status == "Processing") {
      return AppColors.appGreenColor;
    }
  }

  checkFontColorColor(dynamic status) {
    if (status == "Please Pay") {
      return AppColors.appWhiteColor;
    } else if (status == "Information Need") {
      return AppColors.appWhiteColor;
    } else if (status == "Processing") {
      return AppColors.appWhiteColor;
    }
  }
}
