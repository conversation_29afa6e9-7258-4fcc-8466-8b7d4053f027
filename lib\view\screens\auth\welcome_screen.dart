import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../utils/app_colors.dart';
import 'signin_screen.dart';
import 'signup_screen.dart';

class WelcomeScreen extends StatelessWidget {
  static const String routeName = "/welcomeScreen";
  const WelcomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appPrimaryColor,
      body: Stack(
        children: [
          // Background Image
          // Image.asset(
          //   'assets/images/welcome.png', // Replace with your image path
          //   fit: BoxFit.cover,
          //   width: double.infinity,
          //   height: double.infinity,
          // ),

          // Your Content
          Center(
            child: SafeArea(
              child: ListView(
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: 30.w, vertical: 30.h),
                        child: Row(
                          children: [
                            Icon(
                              Icons.arrow_back_outlined,
                              color: AppColors.appWhiteColor,
                              size: 20.h,
                            ),
                            SizedBox(
                              width: 84.w,
                            ),
                            Text(
                              "Nestblock",
                              style: TextStyle(
                                  fontSize: 35.sp,
                                  color: AppColors.appWhiteColor,
                                  fontWeight: FontWeight.w500),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                      SizedBox(
                        height: 35.h,
                      ),
                      Image.asset(
                        'assets/images/wlc_img.png', // Replace with your image path
                        fit: BoxFit.cover,
                        height: 250.h,
                      ),
                      SizedBox(
                        height: 69.h,
                      ),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 32.w),
                        child: Align(
                          alignment: Alignment.topLeft,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "Welcome",
                                style: TextStyle(
                                    fontSize: 30.sp,
                                    color: AppColors.appWhiteColor,
                                    fontWeight: FontWeight.w500),
                                textAlign: TextAlign.center,
                              ),
                              SizedBox(
                                height: 12.h,
                              ),
                              Text(
                                "Manage your transfer",
                                style: TextStyle(
                                    fontSize: 17.sp,
                                    color: AppColors.appWhiteColor,
                                    fontWeight: FontWeight.normal),
                                textAlign: TextAlign.start,
                              ),
                              Text(
                                "seamlessly & intuitively",
                                style: TextStyle(
                                    fontSize: 18.sp,
                                    color: AppColors.appWhiteColor,
                                    fontWeight: FontWeight.w500),
                                textAlign: TextAlign.start,
                              ),
                              SizedBox(
                                height: 22.h,
                              ),
                              GestureDetector(
                                onTap: () {
                                  Get.toNamed(SignUpScreen.routeName);
                                },
                                child: Container(
                                  height: 45.h,
                                  width: 326.w,
                                  decoration: BoxDecoration(
                                      border: Border.all(
                                          color: AppColors.appWhiteColor),
                                      borderRadius: BorderRadius.circular(4)),
                                  child: Center(
                                    child: Text(
                                      "Create an account",
                                      style: TextStyle(
                                          fontSize: 20.sp,
                                          color: AppColors.appWhiteColor,
                                          fontWeight: FontWeight.w500),
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(
                                height: 30.h,
                              ),
                              Center(
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      "Already have an account?",
                                      style: TextStyle(
                                          fontSize: 16.sp,
                                          color: AppColors.appWhiteColor,
                                          fontWeight: FontWeight.w500),
                                    ),
                                    GestureDetector(
                                      onTap: () {
                                        Get.toNamed(SignInScreen.routeName);
                                      },
                                      child: Text(
                                        " Sign in",
                                        style: TextStyle(
                                            fontSize: 16.sp,
                                            color: AppColors.appWhiteColor,
                                            fontWeight: FontWeight.w500),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(height: 60.h),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
