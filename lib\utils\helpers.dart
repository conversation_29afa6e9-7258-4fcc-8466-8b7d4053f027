import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:Nestblock/main.dart';
import 'package:Nestblock/utils/app_colors.dart';

class Helpers {
  static showToast(
      {Color? bgColor,
      Color? textColor,
      String? msg,
      ToastGravity? gravity = ToastGravity.CENTER}) {
    return Fluttertoast.showToast(
      msg: msg ?? 'Field must not be empty!',
      toastLength: Toast.LENGTH_SHORT,
      gravity: gravity ?? ToastGravity.CENTER,
      timeInSecForIosWeb: 1,
      backgroundColor: bgColor ?? Colors.red,
      textColor: textColor ?? Colors.white,
      fontSize: 16.0.sp,
    );
  }

  /// hide keyboard automatically when click anywhere in screen
  static hideKeyboard() {
    return FocusManager.instance.primaryFocus?.unfocus();
  }

  /// SHOW VALIDATION ERROR DIALOG
  showValidationErrorDialog({
    String errorText = "Field must not be empty!",
    String title = "Warning!",
    int? durationTime = 3,
    Widget? icon,
    Widget? titleText,
    Widget? messageText,
    Color? textColor,
    Color? bgColor,
    SnackPosition? snackPosition = SnackPosition.TOP,
  }) {
    Get.snackbar(
      title,
      titleText: titleText,
      errorText,
      snackPosition: snackPosition,
      messageText: messageText,
      colorText: textColor ?? AppColors.appWhiteColor,
      backgroundColor: bgColor ?? AppColors.appRedColor,
      duration: Duration(seconds: durationTime!),
    );
  }

  static appLoader({Color? color}) {
    return Center(
        child: CircularProgressIndicator(
            color: color ?? AppColors.appPrimaryColor));
  }

  static showSnackBar({required String msg, Color? bgColor}) {
    final snackBar = SnackBar(
      content: Text(
        msg,
        style: TextStyle(
            color: Colors.white, fontSize: 14.sp, fontFamily: "Dubai"),
      ),
      backgroundColor: bgColor ?? AppColors.appRedColor,
      duration: const Duration(seconds: 2),
      behavior: SnackBarBehavior.floating,
      margin: const EdgeInsets.all(10),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      elevation: 10,
    );

    MyApp.scaffoldMessengerKey.currentState?.showSnackBar(snackBar);
  }
}
