class AppConstants {
  static const String appName = 'NestBlock';
  static const String token = '';

  ///Base URL
  static const String baseUri = 'https://nestblock.net/api/';

  ///End Point
  static const String registerUri = 'signup';
  static const String loginUri = 'login';
  static const String userUri = 'user';
  static const String userUpdateInformationUri = 'updateInformation';
  static const String changePasswordUri = 'changePassword';
  static const String transactionUri = 'transaction/search?';
  static const String paymentUri = 'payment-history/search?';
  static const String twoFaSecurityUri = '2FA-security';
  static const String twoFaSecurityEnableUri = '2FA-security/enable';
  static const String twoFaSecurityDisableUri = '2FA-security/disable';
  static const String ticketListUri = 'support-ticket/list?';
  static const String createTicketUri = 'support-ticket/create';
  static const String viewTicketUri = 'support-ticket/view/';
  static const String replyTicketUri = 'support-ticket/reply';
  static const String transferLogUri = 'transfer-log';
  static const String transferLogDeleteUri = '$transferLogUri/delete';
  static const String countryListUri = 'currencyList';
  static const String sendToReceiverCountryUri = 'toCountry/';
  static const String countryServiceUri = 'countryService';
  static const String moneyCalculationUri = 'moneyCalculation';
  static const String moneyCalculationProceedUri = 'calculationProceed';
  static const String transferFormUri = 'transfer-form';
  static const String submittransferFormUri = 'transfer-formSubmit';
  static const String mailVerifyUri = 'mail-verify';
  static const String smsVerifyUri = 'sms-verify';
  //---------recovery pass-----
  static const String recoveryPasswordGetEmailUri = 'recovery-pass/get-email';
  static const String recoveryPassGetCodeUri = 'recovery-pass/get-code';
  static const String updatePasswordUri = 'update-pass';
  //----------
  static const String notificationConfigUri = 'pusher/config';
  static const String appConfigUri = 'app/config';
  static const String paymentGatewayUri = 'pay-now';
  static const String manualPaymentUri = 'manual/payment/submit';
  static const String cardPaymentUri = 'card/payment';
  static const String otherPaymentUri = 'other/payment';
  static const String paymentDoneUri = 'payment/done';
  static const String languageUri = 'language';
  // ---------------verification-----------
  static const String resendMailUri = 'mail-code';
  static const String resendSmsUri = 'sms-code';
  static const String addressVerifySubmitUri = 'address/submit';
  static const String identityVerifySubmitUri = 'identity/submit';
  static const String identityVerifygetUri = 'verification';
  //------------------
  static const String flutterwaveDataUri = 'flutterwave-data';
  static const String verifyAccountUri = 'verify-account';
  static const String invoiceInfoUri = 'transfer-invoice';
}
