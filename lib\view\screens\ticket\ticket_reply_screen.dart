import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:Nestblock/controller/app_controller.dart';
import 'package:Nestblock/controller/reply_ticket_controller.dart';
import 'package:Nestblock/controller/ticket_list_controller.dart';
import 'package:Nestblock/controller/view_ticket_controller.dart';
import 'package:Nestblock/utils/app_colors.dart';
import 'package:Nestblock/utils/helpers.dart';

import '../../../utils/local_storage.dart';
import '../../widgets/app_button.dart';
import '../../widgets/flexible_space_widget.dart';

class TicketViewReplyScreen extends StatefulWidget {
  static const String routeName = "/ticketViewReplyScreen";
  final String? ticketId;
  const TicketViewReplyScreen({super.key, this.ticketId});

  @override
  State<TicketViewReplyScreen> createState() => _TicketViewReplyScreenState();
}

class _TicketViewReplyScreenState extends State<TicketViewReplyScreen> {
  // final List<ChatMessage> messages = [];

  // Receive the passed data using Get.arguments
  // String ticketId = Get.arguments;

  final TextEditingController _textController = TextEditingController();

  // void _handleSubmitted(String text) {
  //   if (text.isNotEmpty) {
  //     _textController.clear();
  //     setState(() {
  //       messages.add(ChatMessage(text: text, isUser: true));
  //     });
  //   }
  // }

  final List<String> _fileNames = [];

  FilePickerResult? result;
  List<dynamic> selectedFilePaths = []; // Store all selected file paths

  Future<void> _pickFiles() async {
    // Request storage permission
    final storageStatus = await Permission.storage.request();

    if (storageStatus.isGranted) {
      try {
        result = await FilePicker.platform.pickFiles(
          allowMultiple: true,
        );

        if (result != null) {
          setState(() {
            if (kDebugMode) {
              print(result!.paths);
            }
            _fileNames.addAll(result!.paths.map((path) => path!));
            selectedFilePaths.addAll(result!.paths
                .whereType<String>()); // Add selected paths to the list
            print("selected fileNames: " + _fileNames[0]);
            print("selected filePaths: " + selectedFilePaths[0]);
          });
        }
      } catch (e) {
        if (kDebugMode) {
          print("Error while picking files: $e");
        }
      }
    }
  }

  @override
  void initState() {
    Get.find<ViewTicketController>().getViewTicketData(widget.ticketId);
    super.initState();
  }

  bool timeVisible = false;

  int selectedIndex = -1;

  changeDateFormat(dynamic time) {
    DateTime dateTime = DateTime.parse(time);
    return DateFormat('d MMM, yy hh:mm a').format(dateTime);
  }

  @override
  Widget build(BuildContext context) {
    var storedLanguage = LocalStorage.get(LocalStorage.languageData) ?? {};
    return GetBuilder<ViewTicketController>(builder: (viewTicketController) {
      return Scaffold(
        backgroundColor: AppColors.appPrimaryColor,
        appBar: buildAppbar(context, viewTicketController, storedLanguage),
        body: GetBuilder<ViewTicketController>(builder: (viewTicketController) {
          return Column(
            children: [
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(5),
                  width: double.maxFinite,
                  decoration: BoxDecoration(
                      borderRadius:
                          BorderRadius.vertical(top: Radius.circular(20.r)),
                      image: DecorationImage(
                          image: AssetImage("assets/images/chat_bg.jpg"),
                          fit: BoxFit.cover)
                      // color: Color(
                      //     0x80F0F8FF), // Semi-transparent WhatsApp-like chat background color
                      ),
                  child: viewTicketController.isLoading == false
                      ? viewTicketController.message!.messages == null
                          ? Column(children: [])
                          : ListView.builder(
                              shrinkWrap: true,
                              reverse:
                                  true, // To make the chat messages scroll from bottom to top
                              itemCount: viewTicketController
                                  .message!.messages!.length,
                              itemBuilder: (BuildContext context, int index) {
                                return ListTile(
                                  title: Column(
                                    crossAxisAlignment: viewTicketController
                                                .message!
                                                .messages![index]
                                                .adminId ==
                                            null
                                        ? CrossAxisAlignment.end
                                        : CrossAxisAlignment.start,
                                    children: [
                                      GestureDetector(
                                        onTap: () {
                                          setState(() {
                                            timeVisible = !timeVisible;
                                            selectedIndex = index;
                                          });
                                        },
                                        child: viewTicketController
                                                    .message!
                                                    .messages![index]
                                                    .attachments ==
                                                null
                                            ? Container(
                                                width: 200.w,
                                                padding:
                                                    const EdgeInsets.all(8.0),
                                                decoration: BoxDecoration(
                                                  color: viewTicketController
                                                              .message!
                                                              .messages![index]
                                                              .adminId ==
                                                          null
                                                      ? AppColors
                                                          .appPrimaryColor
                                                      : Colors
                                                          .white, // User and admin message bubble color
                                                  borderRadius:
                                                      BorderRadius.only(
                                                    topLeft:
                                                        Radius.circular(10.r),
                                                    topRight:
                                                        Radius.circular(10.r),
                                                    bottomLeft:
                                                        Radius.circular(10.r),
                                                  ),
                                                ),
                                                child: Text(
                                                  viewTicketController.message!
                                                      .messages![index].message
                                                      .toString(),
                                                  style: TextStyle(
                                                    fontSize: 16.sp,

                                                    color: viewTicketController
                                                                .message!
                                                                .messages![
                                                                    index]
                                                                .adminId ==
                                                            null
                                                        ? Colors.white
                                                        : Colors
                                                            .black, // User and admin message text color
                                                  ),
                                                ),
                                              )
                                            : Column(
                                                crossAxisAlignment:
                                                    viewTicketController
                                                                .message!
                                                                .messages![
                                                                    index]
                                                                .adminId ==
                                                            null
                                                        ? CrossAxisAlignment.end
                                                        : CrossAxisAlignment
                                                            .start,
                                                children: [
                                                  Row(
                                                    mainAxisAlignment:
                                                        viewTicketController
                                                                    .message!
                                                                    .messages![
                                                                        index]
                                                                    .adminId ==
                                                                1
                                                            ? MainAxisAlignment
                                                                .start
                                                            : MainAxisAlignment
                                                                .end,
                                                    children: [
                                                      viewTicketController
                                                                  .message!
                                                                  .messages![
                                                                      index]
                                                                  .adminId ==
                                                              1
                                                          ? Container(
                                                              height: 34.h,
                                                              width: 34.h,
                                                              margin: EdgeInsets
                                                                  .only(
                                                                      right:
                                                                          12.w),
                                                              padding:
                                                                  EdgeInsets
                                                                      .all(2),
                                                              decoration:
                                                                  BoxDecoration(
                                                                color: AppColors
                                                                    .appPrimaryColor,
                                                                shape: BoxShape
                                                                    .circle,
                                                              ),
                                                              child: Container(
                                                                decoration: BoxDecoration(
                                                                    shape: BoxShape
                                                                        .circle,
                                                                    image: DecorationImage(
                                                                        image: NetworkImage(viewTicketController
                                                                            .message!
                                                                            .messages![index]
                                                                            .adminImage!))),
                                                              ),
                                                            )
                                                          : SizedBox.shrink(),
                                                      Container(
                                                        padding:
                                                            const EdgeInsets
                                                                .all(8.0),
                                                        decoration:
                                                            BoxDecoration(
                                                          color: viewTicketController
                                                                      .message!
                                                                      .messages![
                                                                          index]
                                                                      .adminId ==
                                                                  null
                                                              ? AppColors
                                                                  .appPrimaryColor
                                                              : Colors
                                                                  .white, // User and admin message bubble color
                                                          borderRadius:
                                                              BorderRadius.only(
                                                            topLeft:
                                                                Radius.circular(
                                                                    10.r),
                                                            topRight:
                                                                Radius.circular(
                                                                    10.r),
                                                            bottomLeft:
                                                                Radius.circular(
                                                                    10.r),
                                                          ),
                                                        ),
                                                        child: Container(
                                                          constraints:
                                                              BoxConstraints(
                                                            maxWidth: MediaQuery
                                                                        .sizeOf(
                                                                            context)
                                                                    .width *
                                                                .5,
                                                          ),
                                                          child: Text(
                                                            viewTicketController
                                                                .message!
                                                                .messages![
                                                                    index]
                                                                .message
                                                                .toString(),
                                                            style: TextStyle(
                                                              fontSize: 16.sp,
                                                              fontFamily:
                                                                  'Dubai',
                                                              color: viewTicketController
                                                                          .message!
                                                                          .messages![
                                                                              index]
                                                                          .adminId ==
                                                                      null
                                                                  ? Colors.white
                                                                  : Colors
                                                                      .black, // User and admin message text color
                                                            ),
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  Container(
                                                    padding:
                                                        const EdgeInsets.all(
                                                            4.0),
                                                    child: Wrap(
                                                      alignment:
                                                          viewTicketController
                                                                      .message!
                                                                      .messages![
                                                                          index]
                                                                      .adminId ==
                                                                  null
                                                              ? WrapAlignment
                                                                  .end
                                                              : WrapAlignment
                                                                  .start,
                                                      runSpacing: 5,
                                                      spacing: 2,
                                                      children:
                                                          viewTicketController
                                                              .message!
                                                              .messages![index]
                                                              .attachments!
                                                              .map<Widget>(
                                                                  (attachment) {
                                                        return InkWell(
                                                          onTap: () async {
                                                            // get the file name from attachment path
                                                            Uri uri = Uri.parse(
                                                                attachment
                                                                    .attachmentPath);
                                                            String filename =
                                                                uri.pathSegments
                                                                    .last;
                                                            Get.find<TicketListController>()
                                                                    .attachmentPath
                                                                    .value =
                                                                attachment
                                                                    .attachmentPath;

                                                            // finally download the file
                                                            await Get.find<
                                                                    TicketListController>()
                                                                .downloadFile(
                                                                    fileUrl:
                                                                        attachment
                                                                            .attachmentPath,
                                                                    fileName:
                                                                        filename,
                                                                    context:
                                                                        context);
                                                          },
                                                          child: Container(
                                                            padding: EdgeInsets
                                                                .symmetric(
                                                                    horizontal:
                                                                        10.w,
                                                                    vertical:
                                                                        5.h),
                                                            decoration:
                                                                BoxDecoration(
                                                              color: AppColors
                                                                  .appPrimaryColor,
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          20.r),
                                                            ),
                                                            child: Obx(
                                                              () => Row(
                                                                mainAxisSize:
                                                                    MainAxisSize
                                                                        .min,
                                                                children: [
                                                                  Get.find<TicketListController>().attachmentPath.value ==
                                                                              attachment
                                                                                  .attachmentPath &&
                                                                          Get.find<TicketListController>()
                                                                              .isDownloadPressed
                                                                              .value
                                                                      ? Text(
                                                                          Get.find<TicketListController>()
                                                                              .downloadCompleted
                                                                              .value,
                                                                          style:
                                                                              TextStyle(
                                                                            fontSize:
                                                                                15.sp,
                                                                            fontFamily:
                                                                                'Dubai',
                                                                            color:
                                                                                AppColors.appWhiteColor,
                                                                            // You can adjust the style as needed
                                                                          ),
                                                                        )
                                                                      : SizedBox
                                                                          .shrink(),
                                                                  Icon(
                                                                    Icons
                                                                        .download,
                                                                    size: 17.h,
                                                                    color: AppColors
                                                                        .appWhiteColor,
                                                                  ),
                                                                  Text(
                                                                    attachment
                                                                        .attachmentName,
                                                                    style:
                                                                        TextStyle(
                                                                      fontSize:
                                                                          15.sp,
                                                                      fontFamily:
                                                                          'Dubai',
                                                                      color: AppColors
                                                                          .appWhiteColor,
                                                                      // You can adjust the style as needed
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                            ),
                                                          ),
                                                        );
                                                      }).toList(),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                      ),
                                      Visibility(
                                          visible: selectedIndex == index,
                                          child: Text(
                                            selectedIndex == index
                                                ? '${changeDateFormat(viewTicketController.message!.messages![index].createdAt.toString())}'
                                                : '',
                                            style: TextStyle(
                                                color: AppColors.appBlackColor,
                                                fontSize: 14.sp),
                                          )),
                                    ],
                                  ),
                                );
                              },
                            )
                      : Center(
                          child: CircularProgressIndicator(
                          color: AppColors.appPrimaryColor,
                        )),
                ),
              ),
              _fileNames.isNotEmpty
                  ? Align(
                      alignment: Alignment.topLeft,
                      child: Container(
                          padding: const EdgeInsets.symmetric(
                              vertical: 2, horizontal: 4),
                          width: double.infinity,
                          decoration:
                              BoxDecoration(color: Colors.purple.shade500),
                          child: Text(
                            "${_fileNames.length} ${storedLanguage['File Selected'] ?? "File Selected"}",
                            style: TextStyle(
                                color: Colors.white,
                                fontSize: 14.sp,
                                fontWeight: FontWeight.w500),
                          )))
                  : const SizedBox.shrink(),
              const Divider(height: 1.0),
              GetBuilder<ReplyTicketController>(
                  builder: (replyTicketController) {
                return viewTicketController.isLoading == true
                    ? SizedBox()
                    : viewTicketController.message == null
                        ? SizedBox()
                        :
                        // viewTicketController.message!.status == "Closed"
                        //     ? Container(
                        //         height: 30.h,
                        //         width: double.maxFinite,
                        //         decoration: BoxDecoration(
                        //           color: AppColors.appRedColor,
                        //         ),
                        //         child: Center(
                        //           child: Text(
                        //             "This ticket has been closed.",
                        //             style: TextStyle(
                        //                 fontFamily: "Dubai",
                        //                 fontSize: 15.sp,
                        //                 color: AppColors.appWhiteColor),
                        //           ),
                        //         ),
                        //       )
                        //     :
                        Container(
                            padding: EdgeInsets.only(bottom: 10.h),
                            decoration: BoxDecoration(
                              color: Theme.of(context).cardColor,
                            ),
                            child: Row(
                              children: [
                                IconButton(
                                  icon: const Icon(Icons.attachment),
                                  onPressed: () {
                                    _pickFiles();
                                  },
                                ),
                                Expanded(
                                  child: TextField(
                                    controller: _textController,
                                    decoration: InputDecoration.collapsed(
                                        hintText:
                                            storedLanguage['Send a Message'] ??
                                                'Send a message'),
                                  ),
                                ),
                                IconButton(
                                  icon: const Icon(Icons.send),
                                  onPressed: replyTicketController.isLoading
                                      ? null
                                      : () {
                                          setState(() {});
                                          if (_textController.text.isNotEmpty) {
                                            if (_fileNames.isEmpty) {
                                              replyTicketController
                                                  .ticketReplyRequest(
                                                viewTicketController.message!.id
                                                    .toString(),
                                                _textController.text.toString(),
                                                1,
                                              )
                                                  .then((value) {
                                                _textController.text = "";
                                                viewTicketController
                                                    .getViewTicketData(
                                                        widget.ticketId);
                                              });
                                            } else {
                                              replyTicketController
                                                  .ticketReplyRequest(
                                                      viewTicketController
                                                          .message!.id
                                                          .toString(),
                                                      _textController.text
                                                          .toString(),
                                                      1,
                                                      result: selectedFilePaths)
                                                  .then((value) {
                                                _textController.text = "";
                                                _fileNames.clear();
                                                selectedFilePaths.clear();
                                                viewTicketController
                                                    .getViewTicketData(
                                                        widget.ticketId);
                                              });
                                            }
                                          } else {
                                            Helpers.showSnackBar(
                                              msg: storedLanguage[
                                                      'Message field is required'] ??
                                                  "Message field is required",
                                            );
                                          }
                                        },
                                ),
                              ],
                            ),
                          );
              }),
            ],
          );
        }),
      );
    });
  }

  PreferredSize buildAppbar(BuildContext context,
      ViewTicketController viewTicketController, storedLanguage) {
    return PreferredSize(
      preferredSize: Size.fromHeight(60.h),
      child: AppBar(
        centerTitle: true,
        backgroundColor: AppColors.appPrimaryColor,
        leading: GestureDetector(
          onTap: () {
            Navigator.pop(context);
          },
          child: Icon(
            Icons.arrow_back,
            color: AppColors.appWhiteColor,
          ),
        ),
        automaticallyImplyLeading: false,
        titleSpacing: 5,
        elevation: 1,
        flexibleSpace: FlexibleSpaceWidget(),
        title: Text(
          viewTicketController.isLoading == false
              ? "${viewTicketController.message!.pageTitle}"
              : "",
          style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
              color: AppColors.appWhiteColor),
        ),
        actions: [
          PopupMenuButton<String>(
            color: Get.find<AppController>().getDarkCardColorDefault(),
            // iconColor: AppColors.appWhiteColor,

            itemBuilder: (BuildContext context) {
              return <PopupMenuEntry<String>>[
                PopupMenuItem<String>(
                  value: storedLanguage['Refresh'] ?? 'Refresh',
                  child: GestureDetector(
                    onTap: () {
                      Get.find<ViewTicketController>()
                          .getViewTicketData(widget.ticketId);
                      Navigator.pop(context);
                    },
                    child: Text(
                      storedLanguage['Refresh'] ?? "Refresh",
                      style: TextStyle(fontSize: 14.sp),
                    ),
                  ),
                ),
                if (viewTicketController.isLoading == false)
                  if (viewTicketController.message!.status != "Closed")
                    PopupMenuItem<String>(
                      value: storedLanguage['Close Ticket'] ?? "Close Ticket",
                      child: GestureDetector(
                        onTap: () {
                          Get.back();
                          openDialg(storedLanguage);
                        },
                        child: Text(
                          storedLanguage['Close Ticket'] ?? "Close Ticket",
                          style: TextStyle(fontSize: 14.sp),
                        ),
                      ),
                    ),
              ];
            },
            onSelected: (String selectedValue) {
              // Handle the selected option
              if (kDebugMode) {
                print('Selected option: $selectedValue');
              }
            },
          ),
        ],
      ),
    );
  }

  openDialg(storedLanguage) {
    return Get.defaultDialog(
      radius: 20,
      contentPadding: EdgeInsets.symmetric(horizontal: 0),
      titlePadding: EdgeInsets.zero,
      title: "",
      content: Container(
        width: double.maxFinite,
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        child: Column(
          children: [
            SizedBox(height: 10.h),
            Text(
                storedLanguage['Are you sure want to close this ticket?'] ??
                    "Are you sure you want to close this ticket?",
                style: TextStyle(
                  fontFamily: "Dubai",
                  fontSize: 15.sp,
                )),
            SizedBox(height: 15.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                /// cancel
                Expanded(
                  child: AppButton(
                    text: storedLanguage['Cancel'] ?? "Cancel",
                    bgColor: Colors.grey,
                    onTap: () => Get.back(),
                  ),
                ),
                SizedBox(width: 30.w),

                /// close
                Expanded(
                  child: AppButton(
                    text: storedLanguage['Close'] ?? "Close",
                    bgColor: AppColors.appSendMoneyRed,
                    textColor: AppColors.appWhiteColor,
                    onTap: () async {
                      print("clicked==========");
                      Get.find<ReplyTicketController>()
                          .ticketReplyRequest(
                              Get.find<ViewTicketController>()
                                  .message!
                                  .id
                                  .toString(),
                              "",
                              2,
                              isCloseTicket: true)
                          .then((value) {
                        Get.find<TicketListController>()
                            .getTicketListData(page: 1);
                      });
                      Get.back();
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
