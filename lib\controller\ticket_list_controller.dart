import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:Nestblock/routes/page_index.dart';
import 'package:dio/dio.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:open_file/open_file.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/material.dart';

import '../data/model/base_model/api_response.dart';
import '../data/model/response/ticket_list_model.dart';
import '../data/repository/ticket_list_repo.dart';
import '../utils/helpers.dart';
import '../view/screens/auth/signin_screen.dart';
import '../view/screens/profile/profile_setting_screen.dart';
import '../view/screens/profile/two_fa_screen.dart';
import '../view/widgets/app_bottom_sheet.dart';
import 'auth_controller.dart';

class TicketListController extends GetxController {
  final TicketListRepo ticketListRepo;

  TicketListController({required this.ticketListRepo});

  dynamic _status;
  TicketListData? _message;
  TicketListData? get message => _message;
  dynamic get status => _status;

  late ScrollController scrollController;
  int page = 1;
  bool isLoadMore = false;
  bool hasNextPage = true;
  bool _isLoading = false;
  bool get isLoading => _isLoading;
  bool isHideNoMoreDataContainer = false;

  Future loadMore() async {
    if (_isLoading == false &&
        isLoadMore == false &&
        hasNextPage == true &&
        scrollController.position.extentAfter < 300) {
      isLoadMore = true;
      update();
      page += 1;
      await getTicketListData(page: page, isLoadMoreRunning: true);
      print("====================page: " + page.toString());
      isLoadMore = false;
      if (hasNextPage == false) {
        await Future.delayed(Duration(seconds: 6));
        isHideNoMoreDataContainer = true;
      }
      update();
    }
  }

  List<Data> allTicketList = []; // List to store all fetched items

  TicketListModel ticketListModel = TicketListModel();
  resetData() {
    allTicketList.clear();
    hasNextPage = true;
    page = 1;
    update();
  }

  /// Get Ticket Data
  Future<dynamic> getTicketListData(
      {dynamic page, bool? isLoadMoreRunning = false}) async {
    print("isLoading============");
    if (isLoadMoreRunning == false) {
      _isLoading = true;
      // if the firstLoad is running then clear the ticket list
      allTicketList = [];
    }
    update();
    ApiResponse apiResponse = await ticketListRepo.getTicketListData(page);

    if (apiResponse.response != null &&
        apiResponse.response!.statusCode == 200) {
      if (isLoadMoreRunning == false) {
        _isLoading = false;
      }
      update();
      if (apiResponse.response!.data != null) {
        print("api response is : ${apiResponse.response!.data}");
        var res = apiResponse.response!.data;
        if (res['message'] == "Email Verification Required") {
          _isLoading = false;
          Helpers.showSnackBar(msg: res['message']);
          await appBottomSheet();
        } else if (res['message'] == "Mobile Verification Required") {
          _isLoading = false;
          Helpers.showSnackBar(msg: res['message']);
          await appBottomSheet(isMailVerification: false);
        } else if (res['message'] == "Two FA Verification Required") {
          _isLoading = false;
          Helpers.showSnackBar(msg: res['message']);
          Get.offAll(TwoFaScreen(isTwofaVerification: true));
        } else if (res['message'] == "Your account has been suspend") {
          _isLoading = false;
          Helpers.showSnackBar(msg: res['message']);
          Get.find<AuthController>().removeUserToken();
          Get.offAllNamed(SignInScreen.routeName);
        } else if (res['message'] == "Identity Verification Required") {
          _isLoading = false;
          Helpers.showSnackBar(msg: res['message']);
          Get.offAll(ProfileSettingScreen(isIdentityVerification: true));
        } else if (res['message'] == "Address Verification Required") {
          _isLoading = false;
          Helpers.showSnackBar(msg: res['message']);
          Get.offAll(ProfileSettingScreen(isAddressVerification: true));
        } else {
          _message = null;
          update();
          final fetchedData = apiResponse.response!.data['message']['data'];
          if (fetchedData.isNotEmpty) {
            ticketListModel =
                TicketListModel.fromJson(apiResponse.response!.data!);
            _message = ticketListModel.message;
            allTicketList.addAll(ticketListModel.message!.data!);
            if (isLoadMoreRunning == false) {
              _isLoading = false;
            }
            print("================isDataEmpty: false");
          } else {
            ticketListModel =
                TicketListModel.fromJson(apiResponse.response!.data!);
            _message = ticketListModel.message;
            allTicketList.addAll(ticketListModel.message!.data!);
            hasNextPage = false;
            if (isLoadMoreRunning == false) {
              _isLoading = false;
            }
            print("================isDataEmpty: true");
          }
        }

        update();
      }
    } else {
      _isLoading = false;
      update();
    }
  }

  // DOWNLOAD FILE FROM TICKET VIEW
  RxBool isDownloadPressed = false.obs;
  RxString attachmentPath = "".obs;
  RxString downloadCompleted = "".obs;

  Future<void> downloadFile({
    BuildContext? context,
    required String fileUrl,
    required String fileName,
  }) async {
    try {
      if (await Permission.storage.request().isGranted) {
        // Get the downloads directory
        final directory = await getExternalStorageDirectory();
        if (directory == null) {
          throw Exception('Could not access external storage');
        }

        // Create a downloads folder if it doesn't exist
        final downloadsPath = path.join(directory.path, 'Download');
        final dir = Directory(downloadsPath);
        if (!await dir.exists()) {
          await dir.create(recursive: true);
        }

        // Create the file path
        final filePath = path.join(downloadsPath, fileName);

        // Download the file with progress tracking
        final response = await http.get(
          Uri.parse(fileUrl),
          headers: {'Accept': 'application/json'},
        ).timeout(Duration(seconds: 30));

        if (response.statusCode == 200) {
          final file = File(filePath);
          await file.writeAsBytes(response.bodyBytes);

          isDownloadPressed.value = false;
          downloadCompleted.value = "100%";
          update();

          if (context != null) {
            ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text("File downloaded successfully")));
          }

          // Open the file
          await OpenFile.open(filePath);
        } else {
          throw Exception('Failed to download file');
        }
      } else {
        if (context != null) {
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(
              content: Text("Need Storage permission for download this file")));
        }
      }
    } catch (e) {
      print('Error downloading file: $e');
      isDownloadPressed.value = false;
      downloadCompleted.value = "";
      update();

      if (context != null) {
        ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text("Error downloading file: $e")));
      }
    }
    update();
  }

  @override
  void onInit() {
    super.onInit();
    getTicketListData(page: page);
    scrollController = ScrollController()..addListener(loadMore);
  }

  @override
  void onClose() {
    scrollController.dispose();
    super.onClose();
  }
}
