import 'dart:io';

import 'package:Nestblock/di_controller_index.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../../controller/app_controller.dart';
import '../../../di_container.dart';
import '../../../utils/app_colors.dart';
import '../../../utils/helpers.dart';
import '../../../utils/local_storage.dart';
import '../../widgets/dash_shimmer.dart';
import '../../widgets/home_data.dart';
import '../../widgets/list_shimmer.dart';
import '../profile/profile_setting_screen.dart';
import '../profile/two_fa_screen.dart';
import '../ticket/support_ticket_screen.dart';
import '../transfer/transfer_log_screen.dart';
import 'payment/payment_log_screen.dart';

class HomeScreen extends StatelessWidget {
  static const String routeName = "/homeScreen";
  HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
      statusBarColor: AppColors.appPrimaryColor,
    ));

    var controller = Get.find<TransactionController>();
    return GetBuilder<AppController>(builder: (_) {
      var storedLanguage = LocalStorage.get(LocalStorage.languageData) ?? {};
      _.getThemeColor();
      return GetBuilder<UserController>(builder: (userController) {
        return Scaffold(
          backgroundColor: _.getDarkBgColor(),
          appBar: buildAppBar(userController, context, storedLanguage),
          body: RefreshIndicator(
            onRefresh: () async {
              controller.resetDataAfterSearching(
                  isFromOnRefreshIndicator: true);
              await controller.getTransactionHistorySearchData("", "", "",
                  page: 1);
            },
            child: ListView(
              controller: Get.find<TransactionController>().scrollController,
              physics: const AlwaysScrollableScrollPhysics(),
              children: [
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 22.w),
                  child: GetBuilder<TransactionController>(
                      builder: (transactionController) {
                    return transactionController.isLoading == false
                        ? transactionController
                                .transactionHistorySearchItems.isEmpty
                            ? Center(
                                child: Image.asset(
                                  "assets/images/empty_transaction.png",
                                  height: 290.h,
                                  width: double.maxFinite,
                                  fit: BoxFit.cover,
                                ),
                              )
                            : Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    storedLanguage['Recent Transaction'] ??
                                        "Recent Transaction",
                                    style: TextStyle(
                                        fontSize: 20.sp,
                                        color: AppColors.appBlackColor,
                                        fontWeight: FontWeight.w500),
                                    textAlign: TextAlign.center,
                                  ),
                                  SizedBox(
                                    height: 15.h,
                                  ),
                                  ListView.builder(
                                    physics:
                                        const NeverScrollableScrollPhysics(),
                                    itemCount: transactionController
                                        .transactionHistorySearchItems.length,
                                    shrinkWrap: true,
                                    itemBuilder: (context, index) {
                                      var data = transactionController
                                          .transactionHistorySearchItems[index];
                                      return Padding(
                                        padding:
                                            const EdgeInsets.only(bottom: 12),
                                        child: Container(
                                          width: double.infinity,
                                          padding: EdgeInsets.symmetric(
                                              vertical: 8.h, horizontal: 6.w),
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(5),
                                            color: Get.find<AppController>()
                                                .getDarkCardColorDefault(),
                                            boxShadow: LocalStorage.get(
                                                            LocalStorage
                                                                .isDark) !=
                                                        null &&
                                                    LocalStorage.get(
                                                            LocalStorage
                                                                .isDark) ==
                                                        true
                                                ? []
                                                : [
                                                    BoxShadow(
                                                      color: Get.find<
                                                              AppController>()
                                                          .getLanguageCardColor(),
                                                      offset: Offset(2, 2),
                                                      blurRadius: 4,
                                                      spreadRadius: 0,
                                                    ),
                                                  ],
                                          ),
                                          child: Row(
                                            children: [
                                              SizedBox(
                                                // color: Colors.red,
                                                width: 240.w,
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.center,
                                                  children: [
                                                    Expanded(
                                                      flex: 1,
                                                      child: ClipRRect(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(5),
                                                        child: Center(
                                                          child: Image.asset(
                                                            data.transactionType ==
                                                                    "+"
                                                                ? "assets/images/outgoing.png"
                                                                : "assets/images/incoming.png", // Corrected image asset path
                                                            height: 32.h,
                                                            width: 32.h,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                    SizedBox(width: 8.w),
                                                    Expanded(
                                                      flex: 3,
                                                      child: Padding(
                                                        padding:
                                                            const EdgeInsets
                                                                .symmetric(
                                                                vertical: 6),
                                                        child: Column(
                                                          crossAxisAlignment:
                                                              CrossAxisAlignment
                                                                  .start,
                                                          mainAxisAlignment:
                                                              MainAxisAlignment
                                                                  .center,
                                                          children: [
                                                            Text(
                                                              "${data.remark}",
                                                              maxLines: 1,
                                                              overflow:
                                                                  TextOverflow
                                                                      .clip,
                                                              style: TextStyle(
                                                                fontSize: 15.sp,
                                                                fontFamily:
                                                                    'Dubai',
                                                                color: AppColors
                                                                    .appBlackColor,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w500,
                                                                height: 1.3,
                                                              ),
                                                            ),
                                                            Text(
                                                              data.time,
                                                              style: TextStyle(
                                                                fontSize: 14,
                                                                fontFamily:
                                                                    'Dubai',
                                                                color: AppColors
                                                                    .appBlack50,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .normal,
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              SizedBox(
                                                width: 80.w,
                                                child: Center(
                                                  child: Text(
                                                    "${data.amount}",

                                                    // "\$" +
                                                    //     data.amount
                                                    //         .toString()
                                                    //         .split(' ')
                                                    //         .first,
                                                    style: TextStyle(
                                                      fontSize: 13.sp,

                                                      color: data.transactionType ==
                                                              "+"
                                                          ? AppColors
                                                              .appSendMoneyRed
                                                          : AppColors
                                                              .appGreenColor, // Adjusted color
                                                      fontWeight:
                                                          FontWeight.w600,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                  if (transactionController.isLoadMore == true)
                                    Padding(
                                        padding: EdgeInsets.only(
                                            top: 10.h, bottom: 40.h),
                                        child: Helpers.appLoader()),
                                ],
                              )
                        : ListShimmer(
                            length: 7,
                          );
                  }),
                ),
              ],
            ),
          ),
        );
      });
    });
  }

  PreferredSize buildAppBar(
      UserController userController, context, storedLanguage) {
    return PreferredSize(
      preferredSize: Platform.isAndroid
          ? Size.fromHeight(MediaQuery.sizeOf(context).height * .33)
          : Size.fromHeight(MediaQuery.sizeOf(context).height * .3),
      child: AppBar(
        backgroundColor: Get.find<AppController>().getDarkBgColor(),
        flexibleSpace: Container(
          height: userController.data == null
              ? Platform.isAndroid
                  ? 310.h
                  : 290.h
              : 255.h,
          width: double.infinity,
          decoration: BoxDecoration(
            color: userController.data == null
                ? Colors.transparent
                : AppColors.appPrimaryColor,
            borderRadius: BorderRadius.vertical(
                bottom:
                    Radius.circular(userController.data == null ? 0 : 16.r)),
          ),
          child: userController.data != null
              ? Stack(
                  children: [
                    Positioned(
                        left: 0,
                        bottom: 30.h,
                        child: SizedBox(
                          width: 150.w,
                          height: 40.h,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              dotGenerator(),
                              dotGenerator(),
                              dotGenerator(),
                              dotGenerator(),
                            ],
                          ),
                        )),
                    Positioned(
                      bottom: 0,
                      right: -35.w,
                      child: Container(
                        alignment: Alignment.centerRight,
                        height: 150.h,
                        width: 150.w,
                        decoration: BoxDecoration(
                            image: DecorationImage(
                          image: AssetImage(
                            "assets/images/round_horiz.png",
                          ),
                          fit: BoxFit.fitHeight,
                        )),
                      ),
                    ),
                    Positioned(
                      bottom: -35.h,
                      right: 50.w,
                      child: Container(
                        height: 150.h,
                        width: 150.w,
                        decoration: BoxDecoration(
                            image: DecorationImage(
                          image: AssetImage(
                            "assets/images/round_verti.png",
                          ),
                          fit: BoxFit.fitWidth,
                        )),
                      ),
                    ),

                    // Positioned(
                    //     top: 90.h,
                    //     right: 25.wß
                    //     child: ClipRRect(
                    //         borderRadius: BorderRadius.circular(50),
                    //         child: Image.asset(
                    //           "assets/images/m_card.png",
                    //           height: 26.h,
                    //           width: 50.w,
                    //           fit: BoxFit.cover,
                    //         ))),
                    // Positioned(
                    //     top: 95.h,
                    //     right: 100.w,
                    //     child: Image.asset(
                    //       "assets/images/dollar.png",
                    //       height: 60.h,
                    //       width: 60.w,
                    //     )),
                    Padding(
                      padding: EdgeInsets.fromLTRB(30.w, 80.h, 30.w, 0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                children: [
                                  ClipOval(
                                      child: CachedNetworkImage(
                                    imageUrl:
                                        "${userController.data!.user!.photo}",
                                    height: 55.0.h,
                                    width: 55.0.h,
                                    fit: BoxFit.cover,
                                    placeholder: (context, url) =>
                                        const CircularProgressIndicator(),
                                    errorWidget: (context, url, error) =>
                                        const Icon(Icons
                                            .error), // Error widget if loading fails
                                  )),
                                  SizedBox(
                                    width: 8.w,
                                  ),
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        storedLanguage['Welcome Back'] ??
                                            "Welcome Back",
                                        style: TextStyle(
                                            fontSize: 16.sp,
                                            color: AppColors.appWhiteColor,
                                            fontWeight: FontWeight.w500),
                                        textAlign: TextAlign.center,
                                      ),
                                      Text(
                                        "${userController.data!.user!.firstname} ${userController.data!.user!.lastname}",
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        style: TextStyle(
                                            fontSize: 18.sp,
                                            color: AppColors.appWhiteColor,
                                            fontWeight: FontWeight.w500),
                                        textAlign: TextAlign.center,
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                              Row(
                                children: [
                                  IconButton(
                                    onPressed: () {
                                      Get.put(PushNotificationController(
                                              notificationRepo: sl()))
                                          .isNotiSeen();
                                    },
                                    icon: Icon(
                                      Icons.notifications_on_sharp,
                                      color: AppColors.appWhiteColor,
                                      size: 25.h,
                                    ),
                                  ),
                                  Obx(() => CircleAvatar(
                                        radius: 7,
                                        backgroundColor:
                                            Get.put(PushNotificationController(
                                                            notificationRepo:
                                                                sl()))
                                                        .isSeen
                                                        .value ==
                                                    false
                                                ? AppColors.appRedColor
                                                : Colors.transparent,
                                      )),
                                ],
                              ),
                            ],
                          ),
                          SizedBox(
                            height: 45.h,
                          ),
                          SizedBox(
                            height: 70.h,
                            // color: Colors.red,
                            child: ListView.builder(
                                shrinkWrap: true,
                                scrollDirection: Axis.horizontal,
                                itemCount: data.length,
                                itemBuilder: (context, index) {
                                  return Padding(
                                    padding: EdgeInsets.only(right: 29.w),
                                    child: GestureDetector(
                                      onTap: () {
                                        if (index == 0) {
                                          Get.to(() => TransferLogScreen());
                                        }
                                        // if(index==1){
                                        //   Get.toNamed(TransactionScreen.routeName);
                                        // }
                                        if (index == 1) {
                                          Get.toNamed(
                                              PaymentLogScreen.routeName);
                                        }
                                        if (index == 2) {
                                          Get.toNamed(
                                              ProfileSettingScreen.routeName);
                                        }
                                        if (index == 3) {
                                          Get.toNamed(TwoFaScreen.routeName);
                                        }
                                        if (index == 4) {
                                          Get.toNamed(
                                              SupportTicketScreen.routeName);
                                        }
                                      },
                                      child: Container(
                                        color: Colors.transparent,
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: [
                                            Container(
                                              height: 35.h,
                                              width: 40.w,
                                              decoration: BoxDecoration(
                                                  color:
                                                      AppColors.appWhiteColor,
                                                  borderRadius:
                                                      BorderRadius.circular(4)),
                                              child: Center(
                                                child: Image.asset(
                                                  "${data[index].image}",
                                                  height: 21.h,
                                                  width: 30.w,
                                                ),
                                              ),
                                            ),
                                            SizedBox(
                                              height: 10.h,
                                            ),
                                            Text(
                                              data[index].title ==
                                                      "Transfer Log"
                                                  ? storedLanguage[
                                                          'Transfer Log'] ??
                                                      "${data[index].title}"
                                                  : data[index].title ==
                                                          "Payment Log"
                                                      ? storedLanguage[
                                                              'Payment Log'] ??
                                                          "${data[index].title}"
                                                      : data[index].title ==
                                                              "Profile Setting"
                                                          ? storedLanguage[
                                                                  'Profile Setting'] ??
                                                              "${data[index].title}"
                                                          : data[index].title ==
                                                                  "2FA Security"
                                                              ? storedLanguage[
                                                                      '2FA Security'] ??
                                                                  "${data[index].title}"
                                                              : data[index]
                                                                          .title ==
                                                                      "Support"
                                                                  ? storedLanguage[
                                                                          'Support'] ??
                                                                      "${data[index].title}"
                                                                  : "${data[index].title}",
                                              style: TextStyle(
                                                  fontSize: 14.sp,
                                                  color:
                                                      AppColors.appWhiteColor,
                                                  fontWeight: FontWeight.w500),
                                              textAlign: TextAlign.center,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  );
                                }),
                          ),
                        ],
                      ),
                    ),
                  ],
                )
              : DashShimmer(),
        ),
      ),
    );
  }

  Row dotGenerator() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        ...List.generate(
            12,
            (index) => Container(
                  height: 2.h,
                  width: 2.h,
                  decoration: BoxDecoration(
                    color: AppColors.appWhiteColor.withOpacity(.15),
                    shape: BoxShape.circle,
                  ),
                )),
      ],
    );
  }
}
