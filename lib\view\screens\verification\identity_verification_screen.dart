import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:Nestblock/controller/verification_controller.dart';
import 'package:Nestblock/utils/app_colors.dart';
import 'package:Nestblock/utils/helpers.dart';
import '../../../controller/app_controller.dart';
import '../../../utils/local_storage.dart';
import '../../widgets/flexible_space_widget.dart';

class IdentityVerificationScreen extends StatelessWidget {
  static const String routeName = "/identityVerificationScreen";
  const IdentityVerificationScreen({super.key});

  @override
  Widget build(BuildContext context) {
    var storedLanguage = LocalStorage.get(LocalStorage.languageData) ?? {};
    Get.find<VerificationController>().getVerification();
    return GetBuilder<VerificationController>(builder: (_) {
      return Scaffold(
        backgroundColor: AppColors.appPrimaryColor,
        appBar: buildAppbar(context, storedLanguage),
        body: Container(
          decoration: BoxDecoration(
            color: Get.find<AppController>().getDarkBgColor(),
            borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
          ),
          child: ListView(
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 20.h),
                child: Form(
                  key: _.formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      _.isLoading
                          ? Helpers.appLoader()
                          : _.isIdentityVerified
                              ? Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Align(
                                      alignment: Alignment.center,
                                      child: Container(
                                        padding: _.identityMessage
                                                .toString()
                                                .contains("pending")
                                            ? EdgeInsets.all(20.h)
                                            : EdgeInsets.zero,
                                        decoration: BoxDecoration(
                                          color: _.identityMessage
                                                  .toString()
                                                  .contains("pending")
                                              ? AppColors
                                                  .appDashBoardTransactionPending
                                              : Colors.transparent,
                                          shape: BoxShape.circle,
                                        ),
                                        child: Image.asset(
                                          _.identityMessage
                                                  .toString()
                                                  .contains("pending")
                                              ? 'assets/images/pending_icon_history.png'
                                              : 'assets/images/verified_icon.png',
                                          height: 90.h,
                                          width: 90.h,
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                    ),
                                    SizedBox(height: 10.h),
                                    Align(
                                      alignment: Alignment.center,
                                      child: Text(
                                        _.identityMessage,
                                        style: TextStyle(
                                          color: _.identityMessage
                                                  .toString()
                                                  .contains("pending")
                                              ? AppColors
                                                  .appDashBoardTransactionPending
                                              : AppColors.appGreenColor,
                                          fontFamily: "Dubai",
                                          fontSize: 18.sp,
                                        ),
                                      ),
                                    ),
                                  ],
                                )
                              : ListView.builder(
                                  shrinkWrap: true,
                                  physics: NeverScrollableScrollPhysics(),
                                  itemCount: _.categoryNameList.length,
                                  itemBuilder: (context, i) {
                                    var data = _.categoryNameList[i];
                                    return RadioListTile(
                                      title: Text(
                                        data.categoryName!,
                                        style: TextStyle(
                                            fontSize: 18.sp,
                                            color: AppColors.appBlackColor,
                                            fontWeight: FontWeight.w400),
                                      ),
                                      value: data.categoryName,
                                      groupValue: _.selectedOption,
                                      onChanged: _.onChanged,
                                    );
                                  }),
                      SizedBox(
                        height: 12.h,
                      ),
                      if (_.selectedCategoryDynamicList.isNotEmpty) ...[
                        ListView.builder(
                          shrinkWrap: true,
                          physics: NeverScrollableScrollPhysics(),
                          itemCount: _.selectedCategoryDynamicList.length,
                          itemBuilder: (context, index) {
                            final dynamicField =
                                _.selectedCategoryDynamicList[index];
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                if (dynamicField.servicesForm!.type == "file")
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Text(
                                            dynamicField
                                                .servicesForm!.fieldLevel!,
                                            style: TextStyle(
                                                fontSize: 18.sp,
                                                color: AppColors.appBlackColor,
                                                fontWeight: FontWeight.normal),
                                          ),
                                          Text(
                                            " (${storedLanguage['Optional'] ?? 'Optional'})",
                                            style: TextStyle(
                                                fontSize: 16.sp,
                                                color: dynamicField
                                                            .servicesForm!
                                                            .validation ==
                                                        'required'
                                                    ? Colors.transparent
                                                    : AppColors.appBlackColor,
                                                fontWeight: FontWeight.normal),
                                          ),
                                        ],
                                      ),
                                      SizedBox(
                                        height: 8.h,
                                      ),
                                      Container(
                                        height: 45.h,
                                        width: double.infinity,
                                        decoration: BoxDecoration(
                                            color: Get.find<AppController>()
                                                .getDarkCardColorDefault(),
                                            borderRadius:
                                                BorderRadius.circular(8),
                                            border: Border.all(
                                                color: LocalStorage.get(
                                                                LocalStorage
                                                                    .isDark) !=
                                                            null &&
                                                        LocalStorage.get(
                                                                LocalStorage
                                                                    .isDark) ==
                                                            true
                                                    ? Colors.grey.shade800
                                                    : AppColors.appBlack10)),
                                        child: Row(
                                          children: [
                                            MaterialButton(
                                              onPressed: () async {
                                                Helpers.hideKeyboard();
                                                await _.pickFiles(dynamicField
                                                    .servicesForm!.fieldName!);
                                              },
                                              child: Text(
                                                storedLanguage['Open Camera'] ??
                                                    "Open Camera",
                                                style: TextStyle(
                                                    color: AppColors.appBlack70,
                                                    fontSize: 14.sp),
                                              ),
                                            ),
                                            SizedBox(width: 5.w),
                                            Container(
                                              width: 1,
                                              color: LocalStorage.get(
                                                              LocalStorage
                                                                  .isDark) !=
                                                          null &&
                                                      LocalStorage.get(
                                                              LocalStorage
                                                                  .isDark) ==
                                                          true
                                                  ? Colors.grey.shade800
                                                  : AppColors.appBlack10,
                                            ),
                                            SizedBox(width: 13.w),
                                            Text(
                                              _.imagePickerResults[dynamicField
                                                          .servicesForm!
                                                          .fieldName] !=
                                                      null
                                                  ? "1 image selected"
                                                  : "No image selected",
                                              style: TextStyle(
                                                  color: _.imagePickerResults[
                                                              dynamicField
                                                                  .servicesForm!
                                                                  .fieldName] !=
                                                          null
                                                      ? AppColors.appGreenColor
                                                      : AppColors.appBlack70,
                                                  fontSize: 16.sp),
                                            )
                                          ],
                                        ),
                                      ),
                                      SizedBox(
                                        height: 16.h,
                                      ),
                                    ],
                                  ),
                                if (dynamicField.servicesForm!.type == "text")
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Text(
                                            dynamicField
                                                .servicesForm!.fieldLevel!,
                                            style: TextStyle(
                                                fontSize: 16.sp,
                                                color: AppColors.appBlackColor,
                                                fontWeight: FontWeight.w400),
                                          ),
                                          Text(
                                            " (${storedLanguage['Optional'] ?? 'Optional'})",
                                            style: TextStyle(
                                                fontSize: 16.sp,
                                                color: dynamicField
                                                            .servicesForm!
                                                            .validation ==
                                                        'required'
                                                    ? Colors.transparent
                                                    : AppColors.appBlackColor,
                                                fontWeight: FontWeight.normal),
                                          ),
                                        ],
                                      ),
                                      SizedBox(
                                        height: 8.h,
                                      ),
                                      TextFormField(
                                        validator: (value) {
                                          // Perform validation based on the 'validation' property
                                          if (dynamicField.servicesForm!
                                                      .validation ==
                                                  "required" &&
                                              value!.isEmpty) {
                                            return "Field is required";
                                          }
                                          return null;
                                        },
                                        onChanged: (v) {
                                          _
                                              .textEditingControllerMap[
                                                  dynamicField
                                                      .servicesForm!.fieldName]!
                                              .text = v;
                                        },
                                        controller: _.textEditingControllerMap[
                                            dynamicField
                                                .servicesForm!.fieldName],
                                        decoration: InputDecoration(
                                          contentPadding:
                                              const EdgeInsets.symmetric(
                                                  vertical: 0, horizontal: 16),
                                          filled:
                                              true, // Fill the background with color
                                          hintStyle: TextStyle(
                                            color: AppColors.appBlack30,
                                          ),
                                          fillColor: Get.find<AppController>()
                                              .getDarkBgTextFieldColorDefault(), // Background color
                                          enabledBorder: OutlineInputBorder(
                                            borderSide: BorderSide(
                                              color: Get.find<AppController>()
                                                  .getDarkBgTextFieldEnableBorderColorDefault(),
                                            ),
                                          ),
                                          focusedBorder: OutlineInputBorder(
                                            borderSide: BorderSide(
                                                color:
                                                    AppColors.appPrimaryColor),
                                          ),
                                        ),
                                        style: TextStyle(
                                            color: AppColors.appBlackColor),
                                      ),
                                      SizedBox(
                                        height: 16.h,
                                      ),
                                    ],
                                  ),
                                if (dynamicField.servicesForm!.type ==
                                    'textarea')
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Text(
                                            dynamicField
                                                .servicesForm!.fieldLevel!,
                                            style: TextStyle(
                                                fontSize: 16.sp,
                                                color: AppColors.appBlackColor,
                                                fontWeight: FontWeight.w400),
                                          ),
                                          Text(
                                            " (${storedLanguage['Optional'] ?? 'Optional'})",
                                            style: TextStyle(
                                                fontSize: 16.sp,
                                                color: dynamicField
                                                            .servicesForm!
                                                            .validation ==
                                                        'required'
                                                    ? Colors.transparent
                                                    : AppColors.appBlackColor,
                                                fontWeight: FontWeight.normal),
                                          ),
                                        ],
                                      ),
                                      SizedBox(
                                        height: 8.h,
                                      ),
                                      TextFormField(
                                        validator: (value) {
                                          if (dynamicField.servicesForm!
                                                      .validation ==
                                                  "required" &&
                                              value!.isEmpty) {
                                            return "Field is required";
                                          }
                                          return null;
                                        },
                                        controller: _.textEditingControllerMap[
                                            dynamicField
                                                .servicesForm!.fieldName],
                                        maxLines: 7,
                                        minLines: 5,
                                        decoration: InputDecoration(
                                          contentPadding:
                                              const EdgeInsets.symmetric(
                                                  vertical: 0, horizontal: 16),
                                          filled: true,
                                          hintStyle: TextStyle(
                                            color: AppColors.appBlack30,
                                          ),
                                          fillColor: Get.find<AppController>()
                                              .getDarkBgTextFieldColorDefault(), // Background color
                                          enabledBorder: OutlineInputBorder(
                                            borderSide: BorderSide(
                                              color: Get.find<AppController>()
                                                  .getDarkBgTextFieldEnableBorderColorDefault(),
                                            ),
                                          ),
                                          focusedBorder: OutlineInputBorder(
                                            borderSide: BorderSide(
                                                color:
                                                    AppColors.appPrimaryColor),
                                          ),
                                        ),
                                        style: TextStyle(
                                            color: AppColors.appBlackColor),
                                      ),
                                      SizedBox(
                                        height: 16.h,
                                      ),
                                    ],
                                  ),
                              ],
                            );
                          },
                        ),
                      ],
                      SizedBox(
                        height: 12.h,
                      ),
                      _.selectedOption.isEmpty
                          ? SizedBox()
                          : Center(
                              child: GestureDetector(
                                onTap: () async {
                                  Helpers.hideKeyboard();
                                  if (_.formKey.currentState!.validate() &&
                                      _.requiredTypeFileList.isEmpty) {
                                    await _.renderDynamicFieldData();
                                    Map<String, dynamic> body = {
                                      "identity_type": _.identityType,
                                    };
                                    body.addAll(_.dynamicData);

                                    if (kDebugMode) {
                                      print("newMap value : $body");
                                    }

                                    await _
                                        .submitVerificationData(
                                            body: body, context: context)
                                        .then((value) {});
                                  } else {
                                    // print(
                                    //     "required type file list===========================: $_.requiredTypeFileList");
                                    Helpers.showSnackBar(
                                        msg:
                                            "Please fill in all required fields.");
                                  }
                                },
                                child: Container(
                                  height: 45.h,
                                  width: double.infinity,
                                  decoration: BoxDecoration(
                                      color: AppColors.appPrimaryColor,
                                      borderRadius: BorderRadius.circular(32)),
                                  child: Center(
                                      child: _.isFormsubmitting
                                          ? Helpers.appLoader(
                                              color: AppColors.appWhiteColor)
                                          : Text(
                                              storedLanguage['Submit'] ??
                                                  "Submit",
                                              style: TextStyle(
                                                  color:
                                                      AppColors.appWhiteColor,
                                                  fontSize: 16.sp,
                                                  fontWeight: FontWeight.w500),
                                            )),
                                ),
                              ),
                            ),
                      SizedBox(
                        height: 10.h,
                      ),
                    ],
                  ),
                ),
              )
            ],
          ),
        ),
      );
    });
  }

  PreferredSize buildAppbar(BuildContext context, storedLanguage) {
    return PreferredSize(
      preferredSize: Size.fromHeight(60.h),
      child: AppBar(
        centerTitle: true,
        backgroundColor: AppColors.appPrimaryColor,
        leading: GestureDetector(
          onTap: () {
            Navigator.pop(context);
          },
          child: Icon(
            Icons.arrow_back,
            color: AppColors.appWhiteColor,
          ),
        ),
        automaticallyImplyLeading: false,
        titleSpacing: 5,
        elevation: 1,
        flexibleSpace: FlexibleSpaceWidget(),
        title: Text(
          storedLanguage['Identity Verification'] ?? "Identity Verification",
          style: TextStyle(
              fontSize: 24.sp,
              color: AppColors.appWhiteColor,
              fontWeight: FontWeight.w500),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}
