import 'package:Nestblock/di_controller_index.dart';

import '../data/model/base_model/api_response.dart';
import '../data/model/response/payment_model.dart';
import '../data/repository/payment_repo.dart';
import '../routes/page_index.dart';
import '../utils/helpers.dart';
import '../view/screens/auth/signin_screen.dart';
import '../view/screens/profile/profile_setting_screen.dart';
import '../view/screens/profile/two_fa_screen.dart';
import '../view/widgets/app_bottom_sheet.dart';
import 'package:Nestblock/data/model/response/payment_model.dart' as payment;

class PaymentHistoryController extends GetxController {
  final PaymentRepo paymentRepo;

  PaymentHistoryController({required this.paymentRepo});

  var selectedDate = ''.obs;

  void setDate(String date) {
    selectedDate.value = date;
    update();
  }

  // Rx<int> page = 1.obs;

  late ScrollController scrollController;

  // double _currentScrollOffset = 0;

  payment.Data? _data;

  payment.Data? get data => _data;

  // void resetPage() {
  //   page.value = 1;
  //   _data = null;
  //   update();
  // }

  // void pageCounter() {
  //   page.value++;
  //   update();
  // }

  // Rx<int> page = 1.obs;
  final transactionId = TextEditingController();
  final dateTimeEditingCtrlr = TextEditingController();

  bool isSearchTapped = false;
  resetDataAfterSearching({isFromOnRefreshIndicator = false}) {
    paymentHistorySearchItems.clear();
    isSearchTapped = true;
    hasNextPage = true;
    page = isFromOnRefreshIndicator == true ? 0 : 1;
    update();
  }

  int page = 1;
  bool isLoadMore = false;
  bool hasNextPage = true;
  bool _isLoading = false;
  bool get isLoading => _isLoading;
  bool isHideNoMoreDataContainer = false;

  PaymentModel paymentModel = PaymentModel();

  List<Datum> paymentHistorySearchItems = []; // List to store all fetched items

  Future loadMore() async {
    if (_isLoading == false &&
        isLoadMore == false &&
        hasNextPage == true &&
        scrollController.position.extentAfter < 300) {
      isLoadMore = true;
      update();
      page += 1;
      await getPaymentHistorySearchData("", "", "",
          page: page, isLoadMoreRunning: true);
      print("====================page: " + page.toString());
      isLoadMore = false;
      if (hasNextPage == false) {
        await Future.delayed(Duration(seconds: 6));
        isHideNoMoreDataContainer = true;
      }
      update();
    }
  }

  Future<dynamic> getPaymentHistorySearchData(
      dynamic name, dynamic status, dynamic dateTime,
      {dynamic page, bool? isLoadMoreRunning = false}) async {
    if (isLoadMoreRunning == false) {
      _isLoading = true;
    }
    print("================isLoading: true");
    update();
    ApiResponse apiResponse = await paymentRepo
        .searchRequestPaymentHistory(name, status, dateTime, page: page);

    if (apiResponse.response != null &&
        apiResponse.response!.statusCode == 200) {
      print("================isLoading: false");
      update();
      if (apiResponse.response!.data != null) {
        print("api response is : ${apiResponse.response!.data}");
        var res = apiResponse.response!.data;
        if (res['message'] == "Email Verification Required") {
          _isLoading = false;
          Helpers.showSnackBar(msg: res['message']);
          await appBottomSheet();
        } else if (res['message'] == "Mobile Verification Required") {
          _isLoading = false;
          Helpers.showSnackBar(msg: res['message']);
          await appBottomSheet(isMailVerification: false);
        } else if (res['message'] == "Two FA Verification Required") {
          _isLoading = false;
          Helpers.showSnackBar(msg: res['message']);
          Get.offAll(TwoFaScreen(isTwofaVerification: true));
        } else if (res['message'] == "Your account has been suspend") {
          _isLoading = false;
          Helpers.showSnackBar(msg: res['message']);
          Get.find<AuthController>().removeUserToken();
          Get.offAllNamed(SignInScreen.routeName);
        } else if (res['message'] == "Identity Verification Required") {
          _isLoading = false;
          Helpers.showSnackBar(msg: res['message']);
          Get.offAll(ProfileSettingScreen(isIdentityVerification: true));
        } else if (res['message'] == "Address Verification Required") {
          _isLoading = false;
          Helpers.showSnackBar(msg: res['message']);
          Get.offAll(ProfileSettingScreen(isAddressVerification: true));
        } else {
          _data = null;
          print("================data: null");
          update();
          final fetchedData = apiResponse.response!.data['data']['data'];
          if (fetchedData.isNotEmpty) {
            paymentModel = PaymentModel.fromJson(apiResponse.response!.data!);
            _data = paymentModel.data;
            paymentHistorySearchItems.addAll(paymentModel.data!.data!);
            if (isLoadMoreRunning == false) {
              _isLoading = false;
            }
            print("================list len: " +
                paymentHistorySearchItems.length.toString());
            print("================isDataEmpty: false");
          } else {
            paymentModel = PaymentModel.fromJson(apiResponse.response!.data!);
            _data = paymentModel.data;
            paymentHistorySearchItems.addAll(paymentModel.data!.data!);
            hasNextPage = false;
            if (isLoadMoreRunning == false) {
              _isLoading = false;
            }
            print("================list len: " +
                paymentHistorySearchItems.length.toString());
            print("================isDataEmpty: true");
          }
          print("================total list len: " +
              paymentHistorySearchItems.length.toString());
        }

        update();
      }
    } else {
      _isLoading = false;
      update();
    }
  }

  @override
  void onInit() {
    super.onInit();
    getPaymentHistorySearchData("", "", "", page: page);
    scrollController = ScrollController()..addListener(loadMore);
    // resetPage();

    // scrollController =  ScrollController()..addListener(_scrollListener);
  }

  // void _scrollListener() {
  //   final data = _data?.data;
  //   final isLoading = this.isLoading;
  //   print("=================scrolling...");

  //   if (!isLoading &&
  //       data!.length >= 10 &&
  //       scrollController.position.pixels ==
  //           scrollController.position.maxScrollExtent) {
  //     _currentScrollOffset =
  //         scrollController.position.pixels; // Save the current scroll offset
  //     pageCounter();
  //     final page = this.page;
  //     getPaymentHistorySearchData("", "", "", page: page.value.toString());
  //     if (kDebugMode) {
  //       print("scrolling");
  //     }
  //   }
  //   update();
  // }

  @override
  void onClose() {
    scrollController.dispose();
    super.onClose();
  }
}
