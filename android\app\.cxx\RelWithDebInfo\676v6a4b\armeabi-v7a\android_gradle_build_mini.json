{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Flutter Projects\\Nestblock\\Nestblock Mobile\\android\\app\\.cxx\\RelWithDebInfo\\676v6a4b\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Flutter Projects\\Nestblock\\Nestblock Mobile\\android\\app\\.cxx\\RelWithDebInfo\\676v6a4b\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}