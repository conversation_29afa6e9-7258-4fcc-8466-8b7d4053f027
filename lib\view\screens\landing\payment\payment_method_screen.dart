import 'package:Nestblock/di_controller_index.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../../../controller/app_controller.dart';
import '../../../../utils/app_colors.dart';
import '../../../../utils/helpers.dart';
import '../../../widgets/custom_searchable_dropdown.dart';
import '../../../../di_container.dart';
import '../../../../utils/local_storage.dart';
import '../../../widgets/flexible_space_widget.dart';
import '../widget/dotted_line_generator.dart';

// ignore: must_be_immutable
class PaymentMethodScreen extends StatefulWidget {
  static const String routeName = "/paymentMethodScreen";
  PaymentMethodScreen({super.key});

  @override
  State<PaymentMethodScreen> createState() => _PaymentMethodScreenState();
}

class _PaymentMethodScreenState extends State<PaymentMethodScreen> {
  // Custom input formatter for allowing both whole numbers and floating-point numbers
  final _numberFormatter = FilteringTextInputFormatter.allow(
    RegExp(
        r'^\d*\.?\d*$'), // Allow any digits, optionally followed by a period and more digits
  );
  @override
  void initState() {
    getData();
    super.initState();
  }

  @override
  void dispose() {
    Get.put(PaymentGatewayController(paymentGatewayRepo: sl())).selectedImage =
        "";
    Get.put(PaymentGatewayController(paymentGatewayRepo: sl())).totalPayable =
        "";
    Get.put(PaymentGatewayController(paymentGatewayRepo: sl())).sendCurrency =
        "";
    Get.put(PaymentGatewayController(paymentGatewayRepo: sl())).selectedValue =
        "";
    super.dispose();
  }

  Future getData() async {
    await Get.put(PaymentGatewayController(paymentGatewayRepo: sl()))
        .getPaymentGatewayList();
    // Get.put(PaymentGatewayController(paymentGatewayRepo: sl()))
    //     .listenRazorPay();
    if (Get.put(PaymentGatewayController(paymentGatewayRepo: sl()))
        .monnifyApiKey
        .isNotEmpty) {
      Get.put(PaymentGatewayController(paymentGatewayRepo: sl())).initMonnify();
    }
    // Get.put(PaymentGatewayController(paymentGatewayRepo: sl())).initPayStack();
  }

  @override
  Widget build(BuildContext context) {
    var storedLanguage = LocalStorage.get(LocalStorage.languageData) ?? {};
    var c = Get.find<PaymentGatewayController>();
    var moneyCalculationProceedControll = Get.put(
        MoneyCalculationProceedController(moneyCalculationProceedRepo: sl()));
    print(c.manualPaymentElementList.length);
    return GetBuilder<PaymentGatewayController>(builder: (controller) {
      return Scaffold(
        backgroundColor: AppColors.appPrimaryColor,
        appBar: buildAppBar(storedLanguage),
        body: Container(
          decoration: BoxDecoration(
            color: Get.find<AppController>().getDarkBgColor(),
            borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
          ),
          child: ListView(
            children: [
              SizedBox(
                height: 30.h,
              ),
              Stack(
                children: [
                  Container(
                    margin: EdgeInsets.symmetric(horizontal: 30.w),
                    height: MediaQuery.sizeOf(context).height * .63,
                    width: double.maxFinite,
                    decoration: BoxDecoration(
                      color: Get.find<AppController>()
                          .getDarkCardColorDefault(isGreyColor: true),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          SizedBox(height: 30.h),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 25.w),
                            child: Column(
                              children: [
                                Row(
                                  children: [
                                    Text(
                                      storedLanguage['Select Gateway'] ??
                                          "Select Gateway",
                                      style: TextStyle(
                                          fontSize: 18.sp,
                                          color: AppColors.appBlackColor,
                                          fontWeight: FontWeight.w400),
                                    ),
                                  ],
                                ),
                                SizedBox(
                                  height: 8.h,
                                ),
                                controller.isLoading
                                    ? SizedBox()
                                    : Container(
                                        // height: 40.h,
                                        decoration: BoxDecoration(
                                          color: Get.find<AppController>()
                                              .getDarkBgColor(),
                                          borderRadius:
                                              BorderRadius.circular(5),
                                        ),
                                        child: Row(
                                          children: [
                                            Padding(
                                              padding:
                                                  EdgeInsets.only(left: 12.w),
                                              child: controller.selectedImage ==
                                                      ""
                                                  ? SizedBox()
                                                  : Image.network(
                                                      controller.selectedImage,
                                                      width: 16.w,
                                                      height: 14.h,
                                                      fit: BoxFit.cover,
                                                      errorBuilder: (context,
                                                              url, error) =>
                                                          new Icon(Icons.error),
                                                    ),
                                            ),
                                            Expanded(
                                              child: CustomSearchableDropDown(
                                                items: controller.gatewayList,
                                                dropdownItemStyle: TextStyle(
                                                  color: Colors.black,
                                                ),
                                                prefixIcon: SizedBox(),
                                                suffixIcon: Icon(
                                                  Icons.arrow_drop_down,
                                                  color:
                                                      AppColors.appBlackColor,
                                                ),
                                                textStyle: TextStyle(
                                                  color: Colors.black,
                                                ),
                                                hintStyle: TextStyle(
                                                  color: Colors.grey,
                                                ),
                                                decoration: BoxDecoration(
                                                    color: Colors.transparent),
                                                label: storedLanguage[
                                                        'Select Gateway'] ??
                                                    'Select Gateway',
                                                labelStyle: TextStyle(
                                                    fontSize: 14.sp,
                                                    fontWeight: FontWeight.w400,
                                                    color: AppColors
                                                        .appBlackColor),
                                                dropDownMenuItems: controller
                                                    .gatewayList
                                                    .map((e) => e.name)
                                                    .toList(),
                                                onChanged: controller.onChanged,
                                              ),
                                              // AppCustomDropDown(
                                              //   height: 50.h,
                                              //   width: double.infinity,
                                              //   items: controller.gatewayList
                                              //       .map((e) => e.name)
                                              //       .toList(),
                                              //   selectedValue:
                                              //       controller.selectedValue,
                                              //   onChanged: controller.onChanged,
                                              //   hint: "Select Gateway",
                                              //   fontSize: 14.sp,
                                              //   hintStyle: TextStyle(fontSize: 14.sp),
                                              // ),
                                            ),
                                          ],
                                        ),
                                      ),
                                SizedBox(
                                  height: 16.h,
                                ),
                                Row(
                                  children: [
                                    Text(
                                      storedLanguage['Amount'] ?? "Amount",
                                      style: TextStyle(
                                          fontSize: 18.sp,
                                          color: AppColors.appBlackColor,
                                          fontWeight: FontWeight.w400),
                                    ),
                                  ],
                                ),
                                SizedBox(
                                  height: 8.h,
                                ),
                                Container(
                                  height: 44.h,
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(5),
                                      color: Get.find<AppController>()
                                          .getDarkBgColor()),
                                  child: TextFormField(
                                    controller:
                                        controller.amountTextEditingCtrlr,
                                    readOnly: true,
                                    keyboardType:
                                        const TextInputType.numberWithOptions(
                                            decimal: true),
                                    inputFormatters: <TextInputFormatter>[
                                      _numberFormatter,
                                    ],
                                    style: TextStyle(
                                        color: AppColors.appBlackColor),
                                    decoration: InputDecoration(
                                        isCollapsed: true,
                                        isDense: true,
                                        border: InputBorder.none,
                                        hintText: "0.0",
                                        hintStyle: TextStyle(
                                            color: AppColors.appBlack30),
                                        contentPadding: EdgeInsets.only(
                                            left: 12.w, bottom: 0.h)),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(
                            height: 40.h,
                          ),
                          DottedLineWidget(sections: 9, width: 5.w),
                          SizedBox(
                            height: 30.h,
                          ),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 25.w),
                            child: Column(
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      storedLanguage['Payment Method'] ??
                                          "Payment Method",
                                      style: TextStyle(
                                          fontSize: 16.sp,
                                          color: AppColors.appBlack70,
                                          fontWeight: FontWeight.normal),
                                    ),
                                    Text(
                                      controller.selectedValue ?? "",
                                      style: TextStyle(
                                          fontSize: 16.sp,
                                          color: AppColors.appBlack70,
                                          fontWeight: FontWeight.normal),
                                    ),
                                  ],
                                ),
                                SizedBox(
                                  height: 12.h,
                                ),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      storedLanguage['Amount'] ?? "Amount",
                                      style: TextStyle(
                                          fontSize: 16.sp,
                                          color: AppColors.appBlack70,
                                          fontWeight: FontWeight.normal),
                                    ),
                                    Text(
                                      "${controller.totalBaseAmountPay} ${controller.sendCurrency}",
                                      style: TextStyle(
                                          fontSize: 16.sp,
                                          color: AppColors.appBlack70,
                                          fontWeight: FontWeight.normal),
                                    ),
                                  ],
                                ),
                                SizedBox(
                                  height: 12.h,
                                ),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      storedLanguage['Charge Amount'] ??
                                          "Charge Amount",
                                      style: TextStyle(
                                          fontSize: 16.sp,
                                          color: AppColors.appBlack70,
                                          fontWeight: FontWeight.normal),
                                    ),
                                    Text(
                                      "${controller.charge_amount} ${controller.sendCurrency}",
                                      style: TextStyle(
                                          fontSize: 16.sp,
                                          color: AppColors.appBlack70,
                                          fontWeight: FontWeight.normal),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          SizedBox(
                            height: 25.h,
                          ),
                          DottedLineWidget(sections: 9, width: 5.w),
                          SizedBox(
                            height: 25.h,
                          ),
                          Padding(
                            padding: EdgeInsets.symmetric(
                              horizontal: 25.w,
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  storedLanguage['Total Payable'] ??
                                      "Total Payable",
                                  style: TextStyle(
                                      fontSize: 16.sp,
                                      color: AppColors.appBlack70,
                                      fontWeight: FontWeight.w500),
                                ),
                                Text(
                                  "${controller.totalPayable} ${controller.sendCurrency}",
                                  style: TextStyle(
                                      fontSize: 16.sp,
                                      color: AppColors.appBlack70,
                                      fontWeight: FontWeight.w500),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Positioned(
                    left: 20.w,
                    bottom: 210.h,
                    child: Container(
                      height: 31.h,
                      width: 31.h,
                      decoration: BoxDecoration(
                          color: LocalStorage.get(LocalStorage.isDark) !=
                                      null &&
                                  LocalStorage.get(LocalStorage.isDark) == true
                              ? Get.find<AppController>().getDarkBgColor()
                              : AppColors.appWhiteColor,
                          borderRadius: BorderRadius.only(
                            topRight: Radius.circular(50.r),
                            bottomRight: Radius.circular(50.r),
                          )),
                    ),
                  ),
                  Positioned(
                    right: 20.w,
                    bottom: 210.h,
                    child: Container(
                      height: 31.h,
                      width: 31.h,
                      decoration: BoxDecoration(
                          color: LocalStorage.get(LocalStorage.isDark) !=
                                      null &&
                                  LocalStorage.get(LocalStorage.isDark) == true
                              ? Get.find<AppController>().getDarkBgColor()
                              : AppColors.appWhiteColor,
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(50.r),
                            bottomLeft: Radius.circular(50.r),
                          )),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 30.h),
              GestureDetector(
                onTap: controller.isLoadingPaymentSheet
                    ? null
                    : () {
                        controller.onPayNowTapped(context);
                      },
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 32.w),
                  child: Container(
                    height: 45.h,
                    width: 326.w,
                    decoration: BoxDecoration(
                        color: AppColors.appPrimaryColor,
                        borderRadius: BorderRadius.circular(4)),
                    child: Center(
                      child: controller.isFormsubmitting ||
                              controller.isLoadingPaymentSheet
                          ? Helpers.appLoader(color: AppColors.appWhiteColor)
                          : Text(
                              storedLanguage['Pay Now'] ?? "Pay Now",
                              style: TextStyle(
                                  fontSize: 18.sp,
                                  color: AppColors.appWhiteColor,
                                  fontWeight: FontWeight.w500),
                            ),
                    ),
                  ),
                ),
              ),
              // Column(
              //   crossAxisAlignment: CrossAxisAlignment.start,
              //   children: [
              //     Center(
              //         child: Stack(
              //       alignment: Alignment.topCenter,
              //       children: [
              //         Image.asset(
              //           "assets/images/payment_bg.png",
              //           height: 452.h,
              //           width: double.infinity,
              //         ),
              //         Padding(
              //           padding: EdgeInsets.symmetric(
              //               horizontal: 45.w, vertical: 20.h),
              //           child: Column(
              //             crossAxisAlignment: CrossAxisAlignment.start,
              //             children: [
              //               Text(
              //                 "Select Gateway",
              //                 style: TextStyle(
              //                     fontSize: 18.sp,
              //
              //                     color: AppColors.appBlackColor,
              //                     fontWeight: FontWeight.w400),
              //               ),
              //               SizedBox(
              //                 height: 10.h,
              //               ),
              //               Container(
              //                 height: 44.h,
              //                 width: 300.w,
              //                 decoration: BoxDecoration(
              //                     borderRadius: BorderRadius.circular(5),
              //                     color: AppColors.appWhiteColor),
              //               ),
              //               SizedBox(
              //                 height: 16.h,
              //               ),
              //               Text(
              //                 "Amount",
              //                 style: TextStyle(
              //                     fontSize: 18.sp,
              //
              //                     color: AppColors.appBlackColor,
              //                     fontWeight: FontWeight.w400),
              //               ),
              //               SizedBox(
              //                 height: 10.h,
              //               ),
              //               Container(
              //                 height: 44.h,
              //                 width: 300.w,
              //                 alignment: Alignment.center,
              //                 decoration: BoxDecoration(
              //                     borderRadius: BorderRadius.circular(5),
              //                     color: AppColors.appWhiteColor),
              //                 child: TextFormField(
              //                   keyboardType: const TextInputType
              //                       .numberWithOptions(
              //                       decimal:
              //                           true), // Allow the number keyboard with decimal point
              //                   inputFormatters: <TextInputFormatter>[
              //                     _numberFormatter, // Use the custom input formatter
              //                   ],
              //                   decoration: InputDecoration(
              //                       isCollapsed: true,
              //                       isDense: true,
              //                       border: InputBorder.none,
              //                       hintText: "0.0",
              //                       hintStyle:
              //                           TextStyle(color: AppColors.appBlack30),
              //                       contentPadding:
              //                           EdgeInsets.only(left: 12.w, bottom: 0.h)),
              //                 ),
              //               ),
              //               SizedBox(
              //                 height: 62.h,
              //               ),
              //               Row(
              //                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //                 children: [
              //                   Text(
              //                     "Payment Method",
              //                     style: TextStyle(
              //                         fontSize: 16.sp,
              //
              //                         color: AppColors.appBlack70,
              //                         fontWeight: FontWeight.normal),
              //                   ),
              //                   Text(
              //                     "Paypal",
              //                     style: TextStyle(
              //                         fontSize: 16.sp,
              //
              //                         color: AppColors.appBlack70,
              //                         fontWeight: FontWeight.normal),
              //                   ),
              //                 ],
              //               ),
              //               SizedBox(
              //                 height: 12.h,
              //               ),
              //               Row(
              //                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //                 children: [
              //                   Text(
              //                     "Amount",
              //                     style: TextStyle(
              //                         fontSize: 16.sp,
              //
              //                         color: AppColors.appBlack70,
              //                         fontWeight: FontWeight.normal),
              //                   ),
              //                   Text(
              //                     "50 USD",
              //                     style: TextStyle(
              //                         fontSize: 16.sp,
              //
              //                         color: AppColors.appBlack70,
              //                         fontWeight: FontWeight.normal),
              //                   ),
              //                 ],
              //               ),
              //               SizedBox(
              //                 height: 12.h,
              //               ),
              //               Row(
              //                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //                 children: [
              //                   Text(
              //                     "Charge Amount",
              //                     style: TextStyle(
              //                         fontSize: 16.sp,
              //
              //                         color: AppColors.appBlack70,
              //                         fontWeight: FontWeight.normal),
              //                   ),
              //                   Text(
              //                     "10.50 USD",
              //                     style: TextStyle(
              //                         fontSize: 16.sp,
              //
              //                         color: AppColors.appBlack70,
              //                         fontWeight: FontWeight.normal),
              //                   ),
              //                 ],
              //               ),
              //               SizedBox(
              //                 height: 45.h,
              //               ),
              //               Row(
              //                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //                 children: [
              //                   Text(
              //                     "Total Payable",
              //                     style: TextStyle(
              //                         fontSize: 16.sp,
              //
              //                         color: AppColors.appBlack70,
              //                         fontWeight: FontWeight.w500),
              //                   ),
              //                   Text(
              //                     "100.50 USD",
              //                     style: TextStyle(
              //                         fontSize: 16.sp,
              //
              //                         color: AppColors.appBlack70,
              //                         fontWeight: FontWeight.w500),
              //                   ),
              //                 ],
              //               ),
              //             ],
              //           ),
              //         ),
              //       ],
              //     )),
              //     SizedBox(
              //       height: 40.h,
              //     ),
              //     GestureDetector(
              //       onTap: () {},
              //       child: Padding(
              //         padding: EdgeInsets.symmetric(horizontal: 32.w),
              //         child: Container(
              //           height: 45.h,
              //           width: 326.w,
              //           decoration: BoxDecoration(
              //               color: AppColors.appPrimaryColor,
              //               borderRadius: BorderRadius.circular(4)),
              //           child: Center(
              //             child: Text(
              //               "Pay Now",
              //               style: TextStyle(
              //                   fontSize: 18.sp,
              //
              //                   color: AppColors.appWhiteColor,
              //                   fontWeight: FontWeight.w500),
              //             ),
              //           ),
              //         ),
              //       ),
              //     ),
              //     SizedBox(
              //       height: 20.h,
              //     ),
              //   ],
              // ),
            ],
          ),
        ),
      );
    });
  }

  PreferredSize buildAppBar(storedLanguage) {
    return PreferredSize(
      preferredSize: Size.fromHeight(60.h),
      child: AppBar(
        centerTitle: true,
        backgroundColor: AppColors.appPrimaryColor,
        automaticallyImplyLeading: false,
        leading: GestureDetector(
          onTap: () {
            Get.back();
          },
          child: Icon(
            Icons.arrow_back,
            color: AppColors.appWhiteColor,
          ),
        ),
        titleSpacing: 5,
        elevation: 1,
        flexibleSpace: FlexibleSpaceWidget(),
        title: Text(
          storedLanguage['Payment method'] ?? "Payment method",
          style: TextStyle(
              fontSize: 24.sp,
              color: AppColors.appWhiteColor,
              fontWeight: FontWeight.w500),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}
