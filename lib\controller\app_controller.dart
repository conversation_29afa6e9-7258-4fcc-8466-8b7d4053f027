import 'package:Nestblock/routes/page_index.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import '../data/model/base_model/api_response.dart';
import '../data/model/response/language_name_model.dart';
import '../data/repository/app_controller_repo.dart';
import '../utils/helpers.dart';
import '../utils/local_storage.dart';
import '../view/screens/auth/signin_screen.dart';
import '../view/screens/profile/profile_setting_screen.dart';
import '../view/screens/profile/two_fa_screen.dart';
import '../view/widgets/app_bottom_sheet.dart';
import 'auth_controller.dart';

class AppController extends GetxController {
  final AppControllerRepo appControllerRepo;

  AppController({required this.appControllerRepo});

  bool _isLoading = false;
  bool get isLoading => _isLoading;
  dynamic res;

  //------------ get app primary color and app config-----------
  Future<dynamic> getAppConfig() async {
    _isLoading = true;
    update();
    ApiResponse apiResponse = await appControllerRepo.getAppConfig();

    if (apiResponse.response != null &&
        apiResponse.response!.statusCode == 200) {
      _isLoading = false;
      res = apiResponse.response!.data;
      update();
      if (res != null) {
        print(res);
        String baseColor = res['data']['baseColor'];
        LocalStorage.write(LocalStorage.flutterwavePayoutStatus,
            "${res['data']['flutterwave_payout_service']}");

        AppColors.appPrimaryColor = HexColor(baseColor);
        update();
      }
    } else {
      _isLoading = false;
      update();
    }
  }

  //-------------- check internet connectivity-----------
  void updateConnectionStatus(List<ConnectivityResult> connectivityResult) {
    if (connectivityResult.contains(ConnectivityResult.none)) {
      Get.dialog(
        const CustomDialog(),
        barrierDismissible:
            false, // Prevent the user from closing the dialog by tapping outside
      );
    } else {
      // Dismiss the dialog if it's currently displayed
      if (Get.isDialogOpen == true) {
        Get.back();
      }
    }
  }

  //---------------change language----------------
  List<Language> languageNameList = [];

  Future<dynamic> getLanguagesList() async {
    update();
    ApiResponse apiResponse = await appControllerRepo.getLanguageNameList();
    languageNameList = [];
    if (apiResponse.response != null &&
        apiResponse.response!.statusCode == 200) {
      update();
      if (apiResponse.response!.data != null) {
        _isLoading = false;
        var res = apiResponse.response!.data;
        if (res['message'] == "Email Verification Required") {
          _isLoading = false;
          Helpers.showSnackBar(msg: res['message']);
          await appBottomSheet();
        } else if (res['message'] == "Mobile Verification Required") {
          _isLoading = false;
          Helpers.showSnackBar(msg: res['message']);
          await appBottomSheet(isMailVerification: false);
        } else if (res['message'] == "Two FA Verification Required") {
          _isLoading = false;
          Helpers.showSnackBar(msg: res['message']);
          Get.offAll(TwoFaScreen(isTwofaVerification: true));
        } else if (res['message'] == "Your account has been suspend") {
          _isLoading = false;
          Helpers.showSnackBar(msg: res['message']);
          Get.find<AuthController>().removeUserToken();
          Get.offAllNamed(SignInScreen.routeName);
        } else if (res['message'] == "Identity Verification Required") {
          _isLoading = false;
          Helpers.showSnackBar(msg: res['message']);
          Get.offAll(ProfileSettingScreen(isIdentityVerification: true));
        } else if (res['message'] == "Address Verification Required") {
          _isLoading = false;
          Helpers.showSnackBar(msg: res['message']);
          Get.offAll(ProfileSettingScreen(isAddressVerification: true));
        } else {
          languageNameList.addAll(
              LanguageNameModel.fromJson(apiResponse.response!.data)
                      .data!
                      .languages ??
                  []);
          _isLoading = false;
          update();
        }

        update();
      }
    } else {
      _isLoading = false;
      update();
    }
  }

  Future<dynamic> getLanguagesDataById({required String id}) async {
    update();
    ApiResponse apiResponse =
        await appControllerRepo.getLanguageDataById(id: id);
    if (apiResponse.response != null &&
        apiResponse.response!.statusCode == 200) {
      update();
      if (apiResponse.response!.data != null) {
        _isLoading = false;
        var res = apiResponse.response!.data;
        if (res['message'] == "Email Verification Required") {
          _isLoading = false;

          await appBottomSheet();
        } else if (res['message'] == "Mobile Verification Required") {
          _isLoading = false;
          Helpers.showSnackBar(msg: res['message']);
          await appBottomSheet(isMailVerification: false);
        } else if (res['message'] == "Two FA Verification Required") {
          _isLoading = false;
          Helpers.showSnackBar(msg: res['message']);
          Get.offAll(TwoFaScreen(isTwofaVerification: true));
        } else if (res['message'] == "Your account has been suspend") {
          _isLoading = false;
          Helpers.showSnackBar(msg: res['message']);
          Get.find<AuthController>().removeUserToken();
          Get.offAllNamed(SignInScreen.routeName);
        } else if (res['message'] == "Identity Verification Required") {
          _isLoading = false;
          Helpers.showSnackBar(msg: res['message']);
          Get.offAll(ProfileSettingScreen(isIdentityVerification: true));
        } else if (res['message'] == "Address Verification Required") {
          _isLoading = false;
          Helpers.showSnackBar(msg: res['message']);
          Get.offAll(ProfileSettingScreen(isAddressVerification: true));
        } else {
          if (apiResponse.response!.data['data'] != null &&
              apiResponse.response!.data['data'] is Map) {
            LocalStorage.write(
                LocalStorage.languageData, apiResponse.response!.data['data']);
            _isLoading = false;
            update();
          }
          _isLoading = false;
          update();
        }

        update();
      }
    } else {
      _isLoading = false;
      update();
    }
  }

  //---------------CHANGE APP THEME (DARK/LIGHT)--------------
  ThemeData lightTheme() {
    return ThemeData(
      fontFamily: Get.locale?.languageCode == 'ar' ? 'Cairo' : 'Dubai',
      textTheme: TextTheme(),
      brightness: Brightness.light,
      colorScheme: ColorScheme.fromSeed(seedColor: AppColors.appPrimaryColor),
      inputDecorationTheme: InputDecorationTheme(
        errorBorder: OutlineInputBorder(
          borderSide: BorderSide(color: Colors.red), // Error border color
          borderRadius: BorderRadius.circular(6.r), // Border radius
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderSide: BorderSide(color: Colors.red), // Error border color
          borderRadius: BorderRadius.circular(6.r),
        ),
      ),
      useMaterial3: true,
    );
  }

  ThemeData darkTheme() {
    return ThemeData(
      fontFamily: Get.locale?.languageCode == 'ar' ? 'Cairo' : 'Dubai',
      brightness: Brightness.dark,
      colorScheme: ColorScheme.dark(
        primary: AppColors.appPrimaryColor,
      ),
      inputDecorationTheme: InputDecorationTheme(
        errorBorder: OutlineInputBorder(
          borderSide: BorderSide(color: Colors.red), // Error border color
          borderRadius: BorderRadius.circular(6.r), // Border radius
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderSide: BorderSide(color: Colors.red), // Error border color
          borderRadius: BorderRadius.circular(6.r),
        ),
      ),
      useMaterial3: true,
    );
  }

  bool isDark = false;

  themeManager(value) {
    isDark = value;
    LocalStorage.write(LocalStorage.isDark, isDark);
    getThemeColor();
    update();
  }

  ThemeMode getThemeMode() {
    return LocalStorage.get(LocalStorage.isDark) == null
        ? ThemeMode.light
        : LocalStorage.get(LocalStorage.isDark)
            ? ThemeMode.dark
            : ThemeMode.light;
  }

  getThemeColor() {
    if (LocalStorage.get(LocalStorage.isDark) != null) {
      if (LocalStorage.get(LocalStorage.isDark) == false) {
        AppColors.appBlack07 = Color(0xffEBEDFA);
        AppColors.appBlack10 = Color(0xffDEDEE0);
        AppColors.appBlack20 = Color(0xffC6C7CA);
        AppColors.appBlack30 = Color(0xffAFB0B5);
        AppColors.appBlack40 = Color(0xff97999F);
        AppColors.appBlack50 = Color(0xff7E8188);
        AppColors.appBlack60 = Color(0xff676A73);
        AppColors.appBlack70 = Color(0xff50545E);
        AppColors.appBlackColor = Color(0xff000000);
      } else {
        AppColors.appBlackColor = Color(0xffFFFFFF);
        AppColors.appBlack07 = Color(0xffFFFFFF);
        AppColors.appBlack10 = Color(0xffFFFFFF);
        AppColors.appBlack20 = Color(0xffFFFFFF);
        AppColors.appBlack30 = Color(0xffFFFFFF);
        AppColors.appBlack40 = Color(0xffFFFFFF);
        AppColors.appBlack50 = Color(0xffFFFFFF);
        AppColors.appBlack60 = Color(0xffFFFFFF);
        AppColors.appBlack70 = Color(0xffFFFFFF);
      }
    }
    WidgetsBinding.instance.addPostFrameCallback((_) {
      update();
    });
  }

  Color getDarkBgColor() {
    if (LocalStorage.get(LocalStorage.isDark) != null) {
      if (LocalStorage.get(LocalStorage.isDark) == true) {
        return Color(0xff0E1621);
      } else {
        return Colors.white;
      }
    }
    WidgetsBinding.instance.addPostFrameCallback((_) {
      update();
    });
    return Colors.white;
  }

  Color getDarkCardColorDefault({bool? isGreyColor = false}) {
    if (LocalStorage.get(LocalStorage.isDark) != null) {
      if (LocalStorage.get(LocalStorage.isDark) == true) {
        return Color(0xff17212B);
      } else {
        return isGreyColor == true ? AppColors.appBlack07 : Colors.white;
      }
    }
    WidgetsBinding.instance.addPostFrameCallback((_) {
      update();
    });
    return isGreyColor == true ? AppColors.appBlack07 : Colors.white;
  }

  Color getLanguageCardColor() {
    if (LocalStorage.get(LocalStorage.isDark) != null) {
      if (LocalStorage.get(LocalStorage.isDark) == true) {
        return Color(0xff17212B);
      } else {
        return Colors.grey.shade300;
      }
    }
    WidgetsBinding.instance.addPostFrameCallback((_) {
      update();
    });
    return Colors.grey.shade300;
  }

  Color getDarkBgTextFieldColorDefault({bool? isReverseColor = false}) {
    if (LocalStorage.get(LocalStorage.isDark) != null) {
      if (LocalStorage.get(LocalStorage.isDark) == true) {
        return isReverseColor == true ? Color(0xff0E1621) : Color(0xff17212B);
      } else {
        return AppColors.appInputFieldColor;
      }
    }
    WidgetsBinding.instance.addPostFrameCallback((_) {
      update();
    });
    return AppColors.appInputFieldColor;
  }

  Color getDarkBgTextFieldEnableBorderColorDefault() {
    if (LocalStorage.get(LocalStorage.isDark) != null) {
      if (LocalStorage.get(LocalStorage.isDark) == true) {
        return Color(0xff17212B);
      } else {
        return Colors.transparent;
      }
    }
    WidgetsBinding.instance.addPostFrameCallback((_) {
      update();
    });
    return Colors.transparent;
  }
}

class HexColor extends Color {
  static int _getColor(String hex) {
    String formattedHex = "FF" + hex.toUpperCase().replaceAll("#", "");
    return int.parse(formattedHex, radix: 16);
  }

  HexColor(final String hex) : super(_getColor(hex));
}

class CustomDialog extends StatelessWidget {
  const CustomDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        return false;
      },
      child: AlertDialog(
        content: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Lottie.asset(
              'assets/images/no_internet.json', // Replace with your image path
              height: 150.h,
              width: 150.w,
            ),
            Text(
              'No Internet!!! Please check your connection.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontWeight: FontWeight.w400,
                fontSize: 16.sp,
              ),
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
}
