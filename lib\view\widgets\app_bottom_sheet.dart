import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:Nestblock/utils/helpers.dart';
import '../../controller/app_controller.dart';
import '../../controller/verification_controller.dart';
import '../../utils/app_colors.dart';
import '../../utils/local_storage.dart';
import 'app_button.dart';

Future appBottomSheet({bool? isMailVerification = true, context}) {
  var verificationController = Get.find<VerificationController>();
  var storedLanguage = LocalStorage.get(LocalStorage.languageData) ?? {};
  return Get.bottomSheet(
    Container(
        padding: EdgeInsets.symmetric(horizontal: 15.w),
        height: 350.h,
        width: double.maxFinite,
        decoration: BoxDecoration(
          color: Get.find<AppController>().getDarkBgColor(),
          borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
        ),
        child: SingleChildScrollView(
          child: Column(
            // crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 32.h),
              Text(
                  storedLanguage['Enter 6 Digits Code'] ??
                      "Enter 6 Digits Code",
                  style: TextStyle(
                    fontSize: 20.sp,
                    fontWeight: FontWeight.w500,
                    fontFamily: "Dubai",
                  )),
              SizedBox(height: 20.h),
              Text(
                  isMailVerification == true
                      ? storedLanguage[
                              'Enter the 6 digits code that you received on your email.'] ??
                          "Enter the 6 digits code that you received on your email."
                      : storedLanguage[
                              'Enter the 6 digits code that you received on your number.'] ??
                          "Enter the 6 digits code that you received on your phone number.",
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontFamily: "Dubai",
                    color: AppColors.appBlack50,
                    fontWeight: FontWeight.w400,
                  )),
              SizedBox(height: 40.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  buildTextField(
                      controller: verificationController.field1,
                      onChanged: (v) {
                        if (v.length == 1) {
                          FocusManager.instance.primaryFocus?.nextFocus();
                        }
                      }),
                  buildTextField(
                      controller: verificationController.field2,
                      onChanged: (v) {
                        if (v.length == 1) {
                          FocusManager.instance.primaryFocus?.nextFocus();
                        }
                      }),
                  buildTextField(
                      controller: verificationController.field3,
                      onChanged: (v) {
                        if (v.length == 1) {
                          FocusManager.instance.primaryFocus?.nextFocus();
                        }
                      }),
                  buildTextField(
                      controller: verificationController.field4,
                      onChanged: (v) {
                        if (v.length == 1) {
                          FocusManager.instance.primaryFocus?.nextFocus();
                        }
                      }),
                  buildTextField(
                      controller: verificationController.field5,
                      onChanged: (v) {
                        if (v.length == 1) {
                          FocusManager.instance.primaryFocus?.nextFocus();
                        }
                      }),
                  buildTextField(
                      controller: verificationController.field6,
                      onChanged: (v) {}),
                ],
              ),
              SizedBox(height: 16.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                      storedLanguage['Did\'nt recieve any code?'] ??
                          "Didn’t receive any code? ",
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontFamily: "Dubai",
                        color: AppColors.appBlack50,
                        fontWeight: FontWeight.w400,
                      )),
                  InkWell(
                    onTap: () {
                      if (isMailVerification == true) {
                        verificationController.resendMail();
                      } else {
                        verificationController.resendSms();
                      }
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(vertical: 3.h),
                      child:
                          Text(storedLanguage['Resend Code'] ?? "Resend Code",
                              style: TextStyle(
                                fontSize: 16.sp,
                                fontFamily: "Dubai",
                                fontWeight: FontWeight.w400,
                                decoration: TextDecoration.underline,
                              )),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 20.h),
              Obx(
                () => AppButton(
                  buttonWidth: double.maxFinite,
                  onTap: verificationController.isLoading
                      ? null
                      : () async {
                          if (verificationController.field1.text.isEmpty ||
                              verificationController.field2.text.isEmpty ||
                              verificationController.field3.text.isEmpty ||
                              verificationController.field4.text.isEmpty ||
                              verificationController.field5.text.isEmpty ||
                              verificationController.field6.text.isEmpty) {
                            Helpers.showSnackBar(
                              msg: isMailVerification == true
                                  ? "Please enter your email address"
                                  : "Please enter your phone number",
                            );
                          } else {
                            if (isMailVerification == true) {
                              await verificationController.emailVerify(
                                  emailCode: verificationController.field1.text
                                          .toString() +
                                      verificationController.field2.text
                                          .toString() +
                                      verificationController.field3.text
                                          .toString() +
                                      verificationController.field4.text
                                          .toString() +
                                      verificationController.field5.text
                                          .toString() +
                                      verificationController.field6.text
                                          .toString());
                              verificationController.field1.clear();
                              verificationController.field2.clear();
                              verificationController.field3.clear();
                              verificationController.field4.clear();
                              verificationController.field5.clear();
                              verificationController.field6.clear();
                            } else {
                              await verificationController.smsVerify(
                                  smsCode: verificationController.field1.text
                                          .toString() +
                                      verificationController.field2.text
                                          .toString() +
                                      verificationController.field3.text
                                          .toString() +
                                      verificationController.field4.text
                                          .toString() +
                                      verificationController.field5.text
                                          .toString() +
                                      verificationController.field6.text
                                          .toString());
                              verificationController.field1.clear();
                              verificationController.field2.clear();
                              verificationController.field3.clear();
                              verificationController.field4.clear();
                              verificationController.field5.clear();
                              verificationController.field6.clear();
                            }
                          }
                        },
                  text: verificationController.isLoading
                      ? "Processing..."
                      : storedLanguage['Continue'] ?? "Continue",
                  bgColor: AppColors.appPrimaryColor,
                  textColor: AppColors.appWhiteColor,
                  textSize: 20.sp,
                ),
              ),
            ],
          ),
        )),
    enableDrag: false,
  );
}

Widget buildTextField(
    {required TextEditingController controller,
    void Function(String)? onChanged}) {
  return Container(
    width: 36.h,
    height: 36.h,
    child: TextField(
      controller: controller,
      onChanged: onChanged,
      textAlign: TextAlign.center,
      style: TextStyle(
        fontSize: 25.sp,
        fontFamily: "Dubai",
        color: AppColors.appBlackColor,
        fontWeight: FontWeight.w400,
      ),
      keyboardType: TextInputType.number,
      inputFormatters: <TextInputFormatter>[
        FilteringTextInputFormatter.digitsOnly,
        LengthLimitingTextInputFormatter(1),
      ],
      decoration: InputDecoration(
        isDense: true,
        isCollapsed: true,
        border: OutlineInputBorder(
          borderSide: BorderSide(color: AppColors.appBlack10),
        ),
      ),
    ),
  );
}
