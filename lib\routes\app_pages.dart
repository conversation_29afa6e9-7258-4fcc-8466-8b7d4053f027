import 'package:Nestblock/routes/page_index.dart';
import '../view/screens/profile/language_screen.dart';

class AppPages {
  static final List<GetPage> appPages = [
    GetPage(
        name: SplashScreen.routeName,
        page: () => SplashScreen(),
        transition: Transition.fade),
    GetPage(
        name: OnbordingScreen.routeName,
        page: () => OnbordingScreen(),
        transition: Transition.fade),
    GetPage(
        name: WelcomeScreen.routeName,
        page: () => WelcomeScreen(),
        transition: Transition.fade),
    GetPage(
        name: SignInScreen.routeName,
        page: () => SignInScreen(),
        transition: Transition.fadeIn),
    GetPage(
        name: SignUpScreen.routeName,
        page: () => SignUpScreen(),
        transition: Transition.fadeIn),
    GetPage(
        name: BottomNavBar.routeName,
        page: () => BottomNavBar(),
        transition: Transition.fadeIn),
    GetPage(
        name: HomeScreen.routeName,
        page: () => HomeScreen(),
        transition: Transition.fadeIn),
    GetPage(
        name: TransactionHistoryScreen.routeName,
        page: () => TransactionHistoryScreen(),
        transition: Transition.fadeIn),
    GetPage(
        name: ProfileSettingScreen.routeName,
        page: () => ProfileSettingScreen(),
        transition: Transition.fadeIn),
    GetPage(
        name: ProfileEditScreen.routeName,
        page: () => ProfileEditScreen(),
        transition: Transition.fadeIn),
    GetPage(
        name: PasswordSettingScreen.routeName,
        page: () => PasswordSettingScreen(),
        transition: Transition.fadeIn),
    GetPage(
        name: TwoFaScreen.routeName,
        page: () => TwoFaScreen(),
        transition: Transition.fadeIn),
    GetPage(
        name: SupportTicketScreen.routeName,
        page: () => SupportTicketScreen(),
        transition: Transition.fadeIn),
    GetPage(
        name: CreateTicketScreen.routeName,
        page: () => CreateTicketScreen(),
        transition: Transition.fadeIn),
    GetPage(
        name: TicketViewReplyScreen.routeName,
        page: () => TicketViewReplyScreen(),
        transition: Transition.fadeIn),
    GetPage(
        name: TransactionScreen.routeName,
        page: () => TransactionScreen(),
        transition: Transition.fadeIn),
    GetPage(
        name: PaymentScreen.routeName,
        page: () => PaymentScreen(),
        transition: Transition.fadeIn),
    GetPage(
        name: PaymentPreviewScreen.routeName,
        page: () => PaymentPreviewScreen(),
        transition: Transition.fadeIn),
    GetPage(
        name: RecipientsDetailsScreen.routeName,
        page: () => RecipientsDetailsScreen(),
        transition: Transition.fadeIn),
    GetPage(
        name: PaymentMethodScreen.routeName,
        page: () => PaymentMethodScreen(),
        transition: Transition.fadeIn),
    GetPage(
        name: IdentityVerificationScreen.routeName,
        page: () => IdentityVerificationScreen(),
        transition: Transition.fadeIn),
    GetPage(
        name: AddressVerificationScreen.routeName,
        page: () => AddressVerificationScreen(),
        transition: Transition.fadeIn),
    GetPage(
        name: PaymentLogScreen.routeName,
        page: () => PaymentLogScreen(),
        transition: Transition.fadeIn),
    GetPage(
        name: TransferLogScreen.routeName,
        page: () => TransferLogScreen(),
        transition: Transition.fadeIn),
    GetPage(
        name: NotificationScreen.routeName,
        page: () => NotificationScreen(),
        transition: Transition.fadeIn),
    GetPage(
        name: DepositPreviewScreen.routeName,
        page: () => DepositPreviewScreen(),
        transition: Transition.fadeIn),
    GetPage(
        name: CardPaymentScreen.routeName,
        page: () => CardPaymentScreen(),
        transition: Transition.fadeIn),
    GetPage(
        name: LanguageScreen.routeName,
        page: () => LanguageScreen(),
        transition: Transition.fadeIn),
  ];
}
